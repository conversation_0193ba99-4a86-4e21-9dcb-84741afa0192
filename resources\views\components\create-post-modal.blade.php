<!-- Create Post Modal -->
<div id="create-post-modal" class="fixed inset-0 z-50 overflow-y-auto hidden" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <!-- Background overlay -->
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onclick="closeCreatePostModal()"></div>

        <!-- This element is to trick the browser into centering the modal contents. -->
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

        <!-- Modal panel -->
        <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full">
            
            <!-- Modal Header -->
            <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                        Create Post
                    </h3>
                    <button onclick="closeCreatePostModal()" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <!-- User Info -->
                <div class="flex items-center space-x-3 mb-4">
                    <img class="h-10 w-10 rounded-full" src="{{ auth()->user()->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url(auth()->user()->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode(auth()->user()->name) . '&color=7BC74D&background=EEEEEE' }}" alt="{{ auth()->user()->name }}">
                    <div>
                        <p class="font-medium text-gray-900">{{ auth()->user()->name }}</p>
                        <select name="organization_id" class="text-sm text-gray-600 border-0 bg-transparent focus:ring-0 p-0">
                            <option value="">Personal Post</option>
                            @foreach(auth()->user()->activeOrganizations()->get() as $org)
                                <option value="{{ $org->id }}">{{ $org->name }}</option>
                            @endforeach
                        </select>
                    </div>
                </div>

                <!-- Form -->
                <form id="create-post-form" action="{{ route('posts.store') }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    
                    <!-- Post Type -->
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Post Type</label>
                        <select name="type" class="w-full border-gray-300 rounded-md shadow-sm focus:ring-custom-green focus:border-custom-green">
                            <option value="general">General</option>
                            <option value="announcement">Announcement</option>
                            <option value="event">Event</option>
                            <option value="financial_report">Financial Report</option>
                        </select>
                    </div>

                    <!-- Title -->
                    <div class="mb-4">
                        <input name="title" type="text" placeholder="What's the title of your post?" class="w-full border-gray-300 rounded-md shadow-sm focus:ring-custom-green focus:border-custom-green text-lg" required>
                    </div>

                    <!-- Content -->
                    <div class="mb-4">
                        <textarea name="content" placeholder="What's on your mind?" rows="4" class="w-full border-gray-300 rounded-md shadow-sm focus:ring-custom-green focus:border-custom-green resize-none" required></textarea>
                    </div>

                    <!-- Image Upload -->
                    <div class="mb-4">
                        <div class="flex items-center space-x-4">
                            <label class="flex items-center space-x-2 cursor-pointer text-custom-green hover:text-custom-second-darkest">
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd" />
                                </svg>
                                <span class="text-sm font-medium">Add Photos</span>
                                <input type="file" onchange="handleImageUpload(event)" multiple accept="image/*" class="hidden" name="images[]">
                            </label>
                        </div>

                        <!-- Image Preview -->
                        <div id="image-preview" class="mt-3 grid grid-cols-2 gap-2 hidden"></div>
                    </div>

                    <!-- File Attachments -->
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">File Attachments</label>
                        <x-file-upload-zone
                            name="attachments"
                            accept=".pdf,.doc,.docx,.txt,.rtf,.jpg,.jpeg,.png,.gif,.webp,.mp4,.avi,.mov,.zip,.rar,.7z"
                            :multiple="true"
                            max-size="10MB"
                            :allowed-types="['pdf', 'doc', 'docx', 'txt', 'rtf', 'jpg', 'jpeg', 'png', 'gif', 'webp', 'mp4', 'avi', 'mov', 'zip', 'rar', '7z']"
                            title="Upload Files"
                            description="Drag and drop files here or click to browse (PDF, DOC, images, videos, archives - max 10MB)" />
                    </div>

                    <!-- Facebook Embed URL -->
                    <div class="mb-4">
                        <input name="facebook_embed_url" type="url" placeholder="Facebook embed URL (optional)" class="w-full border-gray-300 rounded-md shadow-sm focus:ring-custom-green focus:border-custom-green">
                    </div>

                    <!-- Hidden Fields -->
                    <input type="hidden" name="status" value="published">
                    <input type="hidden" name="is_pinned" value="false">

                    <!-- Error Messages -->
                    <div id="error-messages" class="mb-4 bg-red-50 border border-red-200 rounded-md p-3 hidden"></div>

                    <!-- Modal Footer -->
                    <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse -mx-4 -mb-4 mt-6">
                        <button type="submit" id="submit-btn" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-custom-green text-base font-medium text-white hover:bg-custom-second-darkest focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-custom-green sm:ml-3 sm:w-auto sm:text-sm">
                            <span id="submit-text">Post</span>
                            <span id="loading-text" class="hidden flex items-center">
                                <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                Posting...
                            </span>
                        </button>
                        <button onclick="closeCreatePostModal()" type="button" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-custom-green sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                            Cancel
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function handleImageUpload(event) {
    const files = Array.from(event.target.files);
    const preview = document.getElementById('image-preview');
    preview.innerHTML = '';

    if (files.length > 0) {
        preview.classList.remove('hidden');

        files.forEach((file, index) => {
            if (file.type.startsWith('image/')) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const div = document.createElement('div');
                    div.className = 'relative';
                    div.innerHTML = `
                        <img src="${e.target.result}" class="w-full h-24 object-cover rounded-lg">
                        <button type="button" onclick="removeImage(${index}, this)" class="absolute top-1 right-1 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600">
                            ×
                        </button>
                    `;
                    preview.appendChild(div);
                };
                reader.readAsDataURL(file);
            }
        });
    } else {
        preview.classList.add('hidden');
    }
}

function removeImage(index, button) {
    const input = document.querySelector('input[name="images[]"]');
    const dt = new DataTransfer();

    Array.from(input.files).forEach((file, i) => {
        if (i !== index) {
            dt.items.add(file);
        }
    });

    input.files = dt.files;
    button.parentElement.remove();

    if (input.files.length === 0) {
        document.getElementById('image-preview').classList.add('hidden');
    }
}

// Handle form submission with AJAX
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('create-post-form');
    if (form) {
        form.addEventListener('submit', async function(e) {
            e.preventDefault();

            const submitBtn = document.getElementById('submit-btn');
            const submitText = document.getElementById('submit-text');
            const loadingText = document.getElementById('loading-text');
            const errorDiv = document.getElementById('error-messages');

            // Show loading state
            submitBtn.disabled = true;
            submitText.classList.add('hidden');
            loadingText.classList.remove('hidden');
            errorDiv.classList.add('hidden');

            try {
                const formData = new FormData(form);

                const response = await fetch(form.action, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.success) {
                        closeCreatePostModal();
                        window.location.reload();
                    } else {
                        const errors = data.errors ? Object.values(data.errors).flat() : ['An error occurred'];
                        errorDiv.innerHTML = errors.map(error => `<p class="text-sm text-red-600">${error}</p>`).join('');
                        errorDiv.classList.remove('hidden');
                    }
                } else if (response.status === 422) {
                    // Handle validation errors
                    const data = await response.json();
                    const errors = data.errors ? Object.values(data.errors).flat() : ['Validation failed'];
                    errorDiv.innerHTML = errors.map(error => `<p class="text-sm text-red-600">${error}</p>`).join('');
                    errorDiv.classList.remove('hidden');
                } else {
                    // Handle other HTTP errors
                    errorDiv.innerHTML = `<p class="text-sm text-red-600">Server error: ${response.status}</p>`;
                    errorDiv.classList.remove('hidden');
                }
            } catch (error) {
                errorDiv.innerHTML = '<p class="text-sm text-red-600">Network error. Please try again.</p>';
                errorDiv.classList.remove('hidden');
    } finally {
        const submitBtn = document.getElementById('submit-btn');
        const submitText = document.getElementById('submit-text');
        const loadingText = document.getElementById('loading-text');

        submitBtn.disabled = false;
        submitText.classList.remove('hidden');
        loadingText.classList.add('hidden');
    }
        });
    }
});

// Simple modal functions
function openCreatePostModal() {
    document.getElementById('create-post-modal').classList.remove('hidden');
}

function closeCreatePostModal() {
    document.getElementById('create-post-modal').classList.add('hidden');
    document.getElementById('create-post-form').reset();
    document.getElementById('image-preview').innerHTML = '';
    document.getElementById('image-preview').classList.add('hidden');
    document.getElementById('error-messages').classList.add('hidden');

    // Reset file upload zones
    const attachmentPreviews = document.querySelectorAll('#create-post-modal [id$="-preview"]');
    attachmentPreviews.forEach(preview => {
        preview.classList.add('hidden');
        preview.innerHTML = '';
    });
}
</script>
