<?php

namespace App\Notifications;

use App\Models\User;
use App\Models\Comment;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\BroadcastMessage;
use Illuminate\Notifications\Notification;

class CommentReplied extends Notification implements ShouldQueue
{
    use Queueable;

    public $user;
    public $parentComment;
    public $reply;

    /**
     * Create a new notification instance.
     */
    public function __construct(User $user, Comment $parentComment, Comment $reply)
    {
        $this->user = $user;
        $this->parentComment = $parentComment;
        $this->reply = $reply;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['database', 'broadcast'];
    }

    /**
     * Get the broadcastable representation of the notification.
     */
    public function toBroadcast(object $notifiable): BroadcastMessage
    {
        return new BroadcastMessage([
            'id' => $this->id,
            'type' => 'comment_replied',
            'user_id' => $this->user->id,
            'user_name' => $this->user->name,
            'user_avatar' => $this->user->getAvatarUrl(64),
            'parent_comment_id' => $this->parentComment->id,
            'reply_id' => $this->reply->id,
            'reply_content' => $this->getReplyPreview(),
            'message' => $this->getMessage(),
            'url' => $this->getCommentUrl(),
            'created_at' => now()->toISOString(),
        ]);
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'type' => 'comment_replied',
            'user_id' => $this->user->id,
            'user_name' => $this->user->name,
            'user_avatar' => $this->user->getNotificationAvatarUrl(),
            'parent_comment_id' => $this->parentComment->id,
            'reply_id' => $this->reply->id,
            'reply_content' => $this->getReplyPreview(),
            'message' => $this->getMessage(),
            'url' => $this->getCommentUrl(),
        ];
    }

    /**
     * Get the notification message
     */
    private function getMessage(): string
    {
        return "{$this->user->name} replied to your comment";
    }

    /**
     * Get reply preview (first 100 characters)
     */
    private function getReplyPreview(): string
    {
        return \Illuminate\Support\Str::limit($this->reply->content, 100);
    }

    /**
     * Get the comment URL
     */
    private function getCommentUrl(): string
    {
        $commentable = $this->parentComment->commentable;
        
        if ($commentable instanceof \App\Models\Post) {
            if ($commentable->group_id) {
                return route('groups.show', $commentable->group->slug) . '#comment-' . $this->reply->id;
            } elseif ($commentable->organization_id) {
                return route('organizations.show', $commentable->organization->slug) . '#comment-' . $this->reply->id;
            } else {
                return route('profile.user', $commentable->user) . '#comment-' . $this->reply->id;
            }
        }
        
        return route('dashboard') . '#comment-' . $this->reply->id;
    }
}
