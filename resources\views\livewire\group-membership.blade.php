<div>
    @auth
        @if($userMembership)
            @if($userMembership->pivot->status === 'pending')
                <span class="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm font-medium">
                    Pending Approval
                </span>
            @elseif($userMembership->pivot->status === 'active')
                <div class="flex items-center space-x-2">
                    <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">
                        {{ ucfirst($userMembership->pivot->role) }}
                    </span>
                    @if($group->created_by !== auth()->id())
                        <button
                            wire:click="leaveGroup"
                            wire:loading.attr="disabled"
                            wire:confirm="Are you sure you want to leave this group?"
                            class="bg-red-600 text-white px-3 py-1 rounded-full text-sm font-medium hover:bg-red-700 disabled:opacity-50">
                            <span wire:loading.remove wire:target="leaveGroup">Leave</span>
                            <span wire:loading wire:target="leaveGroup">Leaving...</span>
                        </button>
                    @endif
                </div>
            @endif
        @else
            <button
                wire:click="joinGroup"
                wire:loading.attr="disabled"
                class="bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 disabled:opacity-50">
                <span wire:loading.remove wire:target="joinGroup">
                    {{ $group->visibility === 'private' ? 'Request to Join' : 'Join Group' }}
                </span>
                <span wire:loading wire:target="joinGroup">
                    {{ $group->visibility === 'private' ? 'Requesting...' : 'Joining...' }}
                </span>
            </button>
        @endif
    @else
        <a href="{{ route('login') }}" class="bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700">
            Login to Join
        </a>
    @endauth
</div>
