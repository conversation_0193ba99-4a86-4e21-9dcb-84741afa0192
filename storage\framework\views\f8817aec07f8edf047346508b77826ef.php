<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['share']));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['share']), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars, $__key, $__value); ?>

<!-- Share Comment Modal -->
<div id="shareCommentModal-<?php echo e($share->id); ?>" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 hidden z-50" onclick="closeShareCommentModal(<?php echo e($share->id); ?>, event)">
    <div class="relative w-full max-w-4xl bg-white rounded-lg shadow-2xl flex flex-col overflow-hidden" style="height: 90vh; max-height: 90vh;" onclick="event.stopPropagation()">
        <!-- Modal Header -->
        <div class="flex items-center justify-between p-4 border-b border-gray-200 flex-shrink-0">
            <h3 class="text-lg font-medium text-gray-900">
                <?php echo e($share->user->name); ?>'s shared post
            </h3>
            <button onclick="closeShareCommentModal(<?php echo e($share->id); ?>)" class="text-gray-400 hover:text-gray-600 transition-colors">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
        </div>

        <!-- Modal Content -->
        <div class="flex flex-1 overflow-hidden">
            <!-- Left Side - Original Post Content -->
            <div class="w-1/2 border-r border-gray-200 flex flex-col">
                <!-- Share Message -->
                <?php if($share->message): ?>
                    <div class="p-4 border-b border-gray-100 bg-gray-50">
                        <div class="flex items-center space-x-3 mb-3">
                            <img class="h-8 w-8 rounded-full" 
                                 src="<?php echo e($share->user->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url($share->user->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($share->user->name) . '&color=7BC74D&background=EEEEEE'); ?>" 
                                 alt="<?php echo e($share->user->name); ?>">
                            <div>
                                <div class="font-medium text-gray-900"><?php echo e($share->user->name); ?></div>
                                <div class="text-sm text-gray-500"><?php echo e($share->created_at->diffForHumans()); ?></div>
                            </div>
                        </div>
                        <p class="text-gray-700"><?php echo nl2br(e($share->message)); ?></p>
                    </div>
                <?php endif; ?>

                <!-- Original Post Content -->
                <div class="flex-1 overflow-y-auto p-4">
                    <div class="bg-white border border-gray-200 rounded-lg overflow-hidden">
                        <!-- Original Post Header -->
                        <div class="p-4 bg-gray-50 border-b border-gray-200">
                            <div class="flex items-center space-x-3">
                                <?php if($share->post->group): ?>
                                    <!-- Group Post Header -->
                                    <div class="flex items-center space-x-2">
                                        <a href="<?php echo e(route('groups.show', $share->post->group)); ?>">
                                            <img class="h-8 w-8 rounded-full"
                                                 src="<?php echo e($share->post->group->logo ? \Illuminate\Support\Facades\Storage::disk('public')->url($share->post->group->logo) : 'https://ui-avatars.com/api/?name=' . urlencode($share->post->group->name) . '&color=3B82F6&background=DBEAFE'); ?>"
                                                 alt="<?php echo e($share->post->group->name); ?>">
                                        </a>
                                        <div class="relative -ml-1">
                                            <a href="<?php echo e(route('profile.user', $share->post->user)); ?>">
                                                <img class="h-6 w-6 rounded-full border-2 border-white"
                                                     src="<?php echo e($share->post->user->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url($share->post->user->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($share->post->user->name) . '&color=7BC74D&background=EEEEEE'); ?>"
                                                     alt="<?php echo e($share->post->user->name); ?>">
                                            </a>
                                        </div>
                                    </div>
                                    <div class="flex-1">
                                        <div class="flex items-center space-x-2">
                                            <a href="<?php echo e(route('groups.show', $share->post->group)); ?>" class="font-medium text-gray-900 hover:text-custom-green">
                                                <?php echo e($share->post->group->name); ?>

                                            </a>
                                        </div>
                                        <div class="flex items-center space-x-1 text-sm text-gray-500">
                                            <a href="<?php echo e(route('profile.user', $share->post->user)); ?>" class="hover:text-custom-green font-medium">
                                                <?php echo e($share->post->user->name); ?>

                                            </a>
                                            <span>•</span>
                                            <span><?php echo e($share->post->published_at->diffForHumans()); ?></span>
                                        </div>
                                    </div>
                                <?php elseif($share->post->organization): ?>
                                    <!-- Organization Post Header -->
                                    <a href="<?php echo e(route('organizations.show', $share->post->organization)); ?>">
                                        <img class="h-8 w-8 rounded-full"
                                             src="<?php echo e($share->post->organization->logo ? \Illuminate\Support\Facades\Storage::disk('public')->url($share->post->organization->logo) : 'https://ui-avatars.com/api/?name=' . urlencode($share->post->organization->name) . '&color=3B82F6&background=DBEAFE'); ?>"
                                             alt="<?php echo e($share->post->organization->name); ?>">
                                    </a>
                                    <div class="flex-1">
                                        <div class="flex items-center space-x-2">
                                            <a href="<?php echo e(route('organizations.show', $share->post->organization)); ?>" class="font-medium text-gray-900 hover:text-custom-green">
                                                <?php echo e($share->post->organization->name); ?>

                                            </a>
                                            <span class="text-gray-500 text-sm">•</span>
                                            <a href="<?php echo e(route('profile.user', $share->post->user)); ?>" class="text-sm text-gray-600 hover:text-custom-green">
                                                by <?php echo e($share->post->user->name); ?>

                                            </a>
                                        </div>
                                        <div class="text-sm text-gray-500">
                                            <?php echo e($share->post->published_at->diffForHumans()); ?>

                                        </div>
                                    </div>
                                <?php else: ?>
                                    <!-- Regular User Post Header -->
                                    <a href="<?php echo e(route('profile.user', $share->post->user)); ?>">
                                        <img class="h-8 w-8 rounded-full"
                                             src="<?php echo e($share->post->user->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url($share->post->user->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($share->post->user->name) . '&color=7BC74D&background=EEEEEE'); ?>"
                                             alt="<?php echo e($share->post->user->name); ?>">
                                    </a>
                                    <div class="flex-1">
                                        <div class="flex items-center space-x-2">
                                            <a href="<?php echo e(route('profile.user', $share->post->user)); ?>" class="font-medium text-gray-900 hover:text-custom-green">
                                                <?php echo e($share->post->user->name); ?>

                                            </a>
                                        </div>
                                        <div class="text-sm text-gray-500">
                                            <?php echo e($share->post->published_at->diffForHumans()); ?>

                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Original Post Content -->
                        <div class="p-4">
                            <h3 class="text-lg font-semibold text-gray-900 mb-2"><?php echo e($share->post->title); ?></h3>
                            
                            <?php if($share->post->content): ?>
                                <div class="text-gray-700 mb-3">
                                    <p><?php echo nl2br(e($share->post->content)); ?></p>
                                </div>
                            <?php endif; ?>

                            <!-- Post Images -->
                            <?php if($share->post->images && count($share->post->images) > 0): ?>
                                <div class="mb-3">
                                    <?php if(count($share->post->images) == 1): ?>
                                        <div class="rounded-lg overflow-hidden">
                                            <img src="<?php echo e(\Illuminate\Support\Facades\Storage::disk('public')->url($share->post->images[0])); ?>" 
                                                 alt="Post image" 
                                                 class="w-full h-64 object-cover">
                                        </div>
                                    <?php else: ?>
                                        <div class="grid grid-cols-2 gap-2 rounded-lg overflow-hidden">
                                            <?php $__currentLoopData = array_slice($share->post->images, 0, 4); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <div class="relative">
                                                    <img src="<?php echo e(\Illuminate\Support\Facades\Storage::disk('public')->url($image)); ?>" 
                                                         alt="Post image <?php echo e($index + 1); ?>" 
                                                         class="w-full h-32 object-cover">
                                                    <?php if($index == 3 && count($share->post->images) > 4): ?>
                                                        <div class="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                                                            <span class="text-white font-semibold">+<?php echo e(count($share->post->images) - 4); ?></span>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>

                            <!-- Post Type Badge -->
                            <?php if($share->post->type !== 'general'): ?>
                                <div class="mb-3">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        <?php if($share->post->type === 'announcement'): ?> bg-blue-100 text-blue-800
                                        <?php elseif($share->post->type === 'event'): ?> bg-green-100 text-green-800
                                        <?php elseif($share->post->type === 'job'): ?> bg-purple-100 text-purple-800
                                        <?php elseif($share->post->type === 'scholarship'): ?> bg-yellow-100 text-yellow-800
                                        <?php else: ?> bg-gray-100 text-gray-800
                                        <?php endif; ?>">
                                        <?php echo e(ucfirst($share->post->type)); ?>

                                    </span>
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- Original Post Interaction Stats -->
                        <div class="px-4 py-2 border-t border-gray-100">
                            <div class="flex items-center justify-between text-sm text-gray-500">
                                <div class="flex items-center space-x-4">
                                    <?php if($share->post->reactions && $share->post->reactions->count() > 0): ?>
                                        <span class="flex items-center space-x-1">
                                            <?php
                                                $reactionCounts = $share->post->reactions->groupBy('type')->map->count()->toArray();
                                                arsort($reactionCounts);
                                                $topReactions = array_slice($reactionCounts, 0, 3, true);
                                            ?>
                                            <?php $__currentLoopData = $topReactions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type => $count): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <?php $details = \App\Models\Reaction::getReactionDetails($type); ?>
                                                <img src="<?php echo e($details['emoji']); ?>" alt="<?php echo e($details['label']); ?>" class="w-4 h-4">
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            <span><?php echo e($share->post->reactions->count()); ?></span>
                                        </span>
                                    <?php endif; ?>
                                    
                                    <?php if($share->post->comments->count() > 0): ?>
                                        <span><?php echo e($share->post->comments->count()); ?> comments</span>
                                    <?php endif; ?>
                                    
                                    <?php if($share->post->shares->count() > 0): ?>
                                        <span><?php echo e($share->post->shares->count()); ?> shares</span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>

                        <!-- Original Post Action Buttons -->
                        <div class="px-4 py-3 border-t border-gray-100">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-6">
                                    <!-- Facebook-style Reactions for Original Post -->
                                    <?php if (isset($component)) { $__componentOriginala21421101ecd1a67ffa8905ddbe200b3 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginala21421101ecd1a67ffa8905ddbe200b3 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.facebook-reactions','data' => ['target' => $share->post,'targetType' => 'post','showCount' => false]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('facebook-reactions'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['target' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($share->post),'target-type' => 'post','show-count' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(false)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginala21421101ecd1a67ffa8905ddbe200b3)): ?>
<?php $attributes = $__attributesOriginala21421101ecd1a67ffa8905ddbe200b3; ?>
<?php unset($__attributesOriginala21421101ecd1a67ffa8905ddbe200b3); ?>
<?php endif; ?>
<?php if (isset($__componentOriginala21421101ecd1a67ffa8905ddbe200b3)): ?>
<?php $component = $__componentOriginala21421101ecd1a67ffa8905ddbe200b3; ?>
<?php unset($__componentOriginala21421101ecd1a67ffa8905ddbe200b3); ?>
<?php endif; ?>

                                    <!-- Comment Button -->
                                    <button onclick="openCommentModal(<?php echo e($share->post->id); ?>)" class="flex items-center space-x-2 text-gray-500 hover:text-blue-600 transition-colors">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                                        </svg>
                                        <span class="text-sm">Comment</span>
                                    </button>

                                    <!-- Share Button -->
                                    <button onclick="openShareModal(<?php echo e($share->post->id); ?>)" class="flex items-center space-x-2 text-gray-500 hover:text-green-600 transition-colors">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                                        </svg>
                                        <span class="text-sm">Share</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Side - Share Comments -->
            <div class="w-1/2 flex flex-col">
                <!-- Share Interaction Stats -->
                <div class="p-4 border-b border-gray-100 bg-gray-50">
                    <div class="flex items-center justify-between text-sm text-gray-500">
                        <div class="flex items-center space-x-4">
                            <div id="modal-share-reaction-summary-<?php echo e($share->id); ?>" class="share-reaction-summary flex items-center space-x-1" style="display: <?php echo e($share->reactions && $share->reactions->count() > 0 ? 'flex' : 'none'); ?>">
                                <?php if($share->reactions && $share->reactions->count() > 0): ?>
                                    <?php
                                        $shareReactionCounts = $share->reactions->groupBy('type')->map->count()->toArray();
                                        arsort($shareReactionCounts);
                                        $topShareReactions = array_slice($shareReactionCounts, 0, 3, true);
                                    ?>
                                    <div id="modal-share-reaction-emojis-<?php echo e($share->id); ?>" class="flex -space-x-1">
                                        <?php $__currentLoopData = $topShareReactions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type => $count): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <?php $details = \App\Models\Reaction::getReactionDetails($type); ?>
                                            <img src="<?php echo e($details['emoji']); ?>" alt="<?php echo e($details['label']); ?>" class="w-4 h-4">
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </div>
                                    <span id="modal-share-reaction-count-<?php echo e($share->id); ?>"><?php echo e($share->reactions->count()); ?></span>
                                <?php endif; ?>
                            </div>

                            <?php if($share->comments->count() > 0): ?>
                                <span id="modal-share-comments-count-<?php echo e($share->id); ?>"><?php echo e($share->comments->count()); ?> comments</span>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Share Action Buttons -->
                <div class="px-4 py-3 border-b border-gray-100">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-6">
                            <!-- Facebook-style Reactions for Share -->
                            <?php if (isset($component)) { $__componentOriginala21421101ecd1a67ffa8905ddbe200b3 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginala21421101ecd1a67ffa8905ddbe200b3 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.facebook-reactions','data' => ['target' => $share,'targetType' => 'share','showCount' => false]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('facebook-reactions'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['target' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($share),'target-type' => 'share','show-count' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(false)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginala21421101ecd1a67ffa8905ddbe200b3)): ?>
<?php $attributes = $__attributesOriginala21421101ecd1a67ffa8905ddbe200b3; ?>
<?php unset($__attributesOriginala21421101ecd1a67ffa8905ddbe200b3); ?>
<?php endif; ?>
<?php if (isset($__componentOriginala21421101ecd1a67ffa8905ddbe200b3)): ?>
<?php $component = $__componentOriginala21421101ecd1a67ffa8905ddbe200b3; ?>
<?php unset($__componentOriginala21421101ecd1a67ffa8905ddbe200b3); ?>
<?php endif; ?>

                            <!-- Comment Button -->
                            <button class="flex items-center space-x-2 text-gray-500 hover:text-blue-600 transition-colors">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                                </svg>
                                <span class="text-sm">Comment</span>
                            </button>

                            <!-- Share Original Post Button -->
                            <button onclick="openShareModal(<?php echo e($share->post->id); ?>)" class="flex items-center space-x-2 text-gray-500 hover:text-green-600 transition-colors">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                                </svg>
                                <span class="text-sm">Share</span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Comments Section -->
                <div class="flex-1 flex flex-col overflow-hidden">
                    <!-- Add Comment Form -->
                    <?php if(auth()->guard()->check()): ?>
                        <div class="p-4 border-b border-gray-50">
                            <form class="share-comment-form" data-share-id="<?php echo e($share->id); ?>" data-parent-id="">
                                <?php echo csrf_field(); ?>
                                <div class="flex space-x-3">
                                    <div class="flex-shrink-0">
                                        <img class="h-10 w-10 rounded-full ring-2 ring-white shadow-sm"
                                             src="<?php echo e(auth()->user()->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url(auth()->user()->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode(auth()->user()->name) . '&color=7BC74D&background=EEEEEE'); ?>"
                                             alt="<?php echo e(auth()->user()->name); ?>">
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <div class="relative">
                                            <textarea name="content" rows="1"
                                                      placeholder="Write a comment..."
                                                      class="w-full px-4 py-3 border border-gray-200 rounded-full shadow-sm focus:ring-2 focus:ring-custom-green/20 focus:border-custom-green resize-none text-sm bg-gray-50 hover:bg-white transition-colors duration-200 placeholder-gray-400"
                                                      required></textarea>
                                            <div class="absolute right-2 top-1/2 transform -translate-y-1/2 opacity-0 transition-opacity duration-200" id="modal-comment-submit-btn-<?php echo e($share->id); ?>">
                                                <button type="submit"
                                                        class="p-2 bg-custom-green text-white rounded-full hover:bg-custom-second-darkest focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-custom-green shadow-sm transition-all duration-200 hover:scale-105">
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"/>
                                                    </svg>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    <?php endif; ?>

                    <!-- Comments List -->
                    <div class="flex-1 overflow-y-auto">
                        <div class="comments-list divide-y divide-gray-100" id="modal-share-comments-list-<?php echo e($share->id); ?>">
                            <?php $__empty_1 = true; $__currentLoopData = $share->comments->whereNull('parent_id'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $comment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <?php if (isset($component)) { $__componentOriginal144b887a0a40fb7550e3c201e0a4811b = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal144b887a0a40fb7550e3c201e0a4811b = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modal-share-comment-item','data' => ['comment' => $comment,'share' => $share]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('modal-share-comment-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['comment' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($comment),'share' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($share)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal144b887a0a40fb7550e3c201e0a4811b)): ?>
<?php $attributes = $__attributesOriginal144b887a0a40fb7550e3c201e0a4811b; ?>
<?php unset($__attributesOriginal144b887a0a40fb7550e3c201e0a4811b); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal144b887a0a40fb7550e3c201e0a4811b)): ?>
<?php $component = $__componentOriginal144b887a0a40fb7550e3c201e0a4811b; ?>
<?php unset($__componentOriginal144b887a0a40fb7550e3c201e0a4811b); ?>
<?php endif; ?>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <div class="no-comments text-gray-500 text-center py-12 px-4">
                                    <div class="max-w-sm mx-auto">
                                        <div class="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
                                            <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                                            </svg>
                                        </div>
                                        <h3 class="text-lg font-medium text-gray-900 mb-2">No comments yet</h3>
                                        <p class="text-gray-500">Be the first to share your thoughts!</p>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Share Comment Modal Enhancements */
.share-comment-form textarea:focus {
    min-height: 100px;
    border-radius: 1rem;
    transition: all 0.3s ease;
    background-color: white;
}

.share-comment-form textarea:focus + div #modal-comment-submit-btn-<?php echo e($share->id); ?> {
    opacity: 1;
}

/* Modal specific styling */
#shareCommentModal-<?php echo e($share->id); ?> .comment-item {
    transition: all 0.2s ease;
}

#shareCommentModal-<?php echo e($share->id); ?> .comment-item:hover {
    background-color: rgba(249, 250, 251, 0.5);
}

/* Scrollbar styling for modal */
#shareCommentModal-<?php echo e($share->id); ?> .overflow-y-auto::-webkit-scrollbar {
    width: 6px;
}

#shareCommentModal-<?php echo e($share->id); ?> .overflow-y-auto::-webkit-scrollbar-track {
    background: #f1f5f9;
}

#shareCommentModal-<?php echo e($share->id); ?> .overflow-y-auto::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
}

#shareCommentModal-<?php echo e($share->id); ?> .overflow-y-auto::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Enhanced comment form interactions for share modal
    const shareCommentForm = document.querySelector('#shareCommentModal-<?php echo e($share->id); ?> .share-comment-form');

    if (shareCommentForm) {
        const textarea = shareCommentForm.querySelector('textarea[name="content"]');
        const submitBtn = shareCommentForm.querySelector('button[type="submit"]');

        if (textarea && submitBtn) {
            // Show/hide submit button based on content
            textarea.addEventListener('input', function() {
                const submitBtnContainer = shareCommentForm.querySelector('[id^="modal-comment-submit-btn-"]');
                if (this.value.trim().length > 0) {
                    if (submitBtnContainer) {
                        submitBtnContainer.style.opacity = '1';
                    }
                } else {
                    if (submitBtnContainer) {
                        submitBtnContainer.style.opacity = '0';
                    }
                }
            });

            // Auto-resize textarea
            textarea.addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = Math.min(this.scrollHeight, 120) + 'px';
            });

            // Focus effects
            textarea.addEventListener('focus', function() {
                this.parentElement.classList.add('focused');
            });

            textarea.addEventListener('blur', function() {
                if (this.value.trim() === '') {
                    this.parentElement.classList.remove('focused');
                }
            });
        }
    }
});
</script>
<?php /**PATH C:\xampp\htdocs\LARAVEL_PROJECTS\lara_unilink\resources\views/components/share-comment-modal.blade.php ENDPATH**/ ?>