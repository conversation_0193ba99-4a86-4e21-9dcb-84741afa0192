<?php

namespace App\Events;

use App\Models\User;
use App\Models\Post;
use App\Models\Reaction;

use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class PostReactionAdded
{
    use Dispatchable, SerializesModels;

    public $user;
    public $post;
    public $reaction;

    /**
     * Create a new event instance.
     */
    public function __construct(User $user, Post $post, Reaction $reaction)
    {
        $this->user = $user;
        $this->post = $post;
        $this->reaction = $reaction;
    }


}
