<?php

namespace App\Listeners;

use App\Events\PostSharedEvent;
use App\Notifications\PostShared;
class SendPostShareNotification
{
    private static $processedEvents = [];

    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(PostSharedEvent $event): void
    {
        // Create a unique key for this event to prevent duplicate processing
        $eventKey = "post_share_{$event->user->id}_{$event->post->id}_{$event->share->id}";

        // Check if this event has already been processed
        if (in_array($eventKey, self::$processedEvents)) {
            return; // Skip duplicate processing
        }

        // Mark this event as processed
        self::$processedEvents[] = $eventKey;

        // Clean up old processed events (keep only last 100)
        if (count(self::$processedEvents) > 100) {
            self::$processedEvents = array_slice(self::$processedEvents, -100);
        }

        // Don't notify if user shared their own post
        if ($event->user->id === $event->post->user_id) {
            return;
        }

        // Check if the post owner wants to receive this type of notification
        if (!$event->post->user->wantsNotification('post_shares')) {
            return;
        }

        // Check for duplicate notifications
        $existingNotification = $event->post->user->notifications()
            ->where('type', 'App\Notifications\PostShared')
            ->whereJsonContains('data->user_id', $event->user->id)
            ->whereJsonContains('data->post_id', $event->post->id)
            ->whereJsonContains('data->share_id', $event->share->id)
            ->where('created_at', '>=', now()->subMinutes(5))
            ->exists();

        if ($existingNotification) {
            return; // Skip duplicate notification
        }

        // Send notification to the post owner
        $event->post->user->notify(new PostShared($event->user, $event->post, $event->share));
    }
}
