@echo off
echo Starting UniLink Notification System...
echo.

echo [1/4] Starting Laravel Reverb WebSocket Server...
start "Reverb Server" cmd /k "php artisan reverb:start"
timeout /t 3 /nobreak >nul

echo [2/4] Starting Queue Worker...
start "Queue Worker" cmd /k "php artisan queue:work"
timeout /t 2 /nobreak >nul

echo [3/4] Starting Vite Development Server...
start "Vite Dev Server" cmd /k "npm run dev"
timeout /t 2 /nobreak >nul

echo [4/4] Starting Laravel Development Server...
start "Laravel Server" cmd /k "php artisan serve"

echo.
echo ✅ All services started successfully!
echo.
echo Services running:
echo - Laravel Server: http://localhost:8000
echo - Reverb WebSocket: ws://localhost:8080
echo - Queue Worker: Processing notifications
echo - Vite Dev Server: Hot reloading assets
echo.
echo To stop all services, close all the opened terminal windows.
echo.
pause
