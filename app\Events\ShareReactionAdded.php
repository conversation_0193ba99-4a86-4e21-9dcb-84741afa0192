<?php

namespace App\Events;

use App\Models\User;
use App\Models\Share;
use App\Models\Reaction;

use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ShareReactionAdded
{
    use Dispatchable, SerializesModels;

    public $user;
    public $share;
    public $reaction;

    /**
     * Create a new event instance.
     */
    public function __construct(User $user, Share $share, Reaction $reaction)
    {
        $this->user = $user;
        $this->share = $share;
        $this->reaction = $reaction;
    }
}
