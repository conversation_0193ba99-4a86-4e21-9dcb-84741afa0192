<?php

namespace App\Listeners;

use App\Events\ShareReactionAdded;
use App\Notifications\ShareReacted;

class SendShareReactionNotification
{
    private static $processedEvents = [];

    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(ShareReactionAdded $event): void
    {
        // Create a unique key for this event to prevent duplicates
        $eventKey = "share_reaction_{$event->user->id}_{$event->share->id}_{$event->reaction->type}_" . now()->timestamp;

        // Check if we've already processed this event
        if (in_array($eventKey, self::$processedEvents)) {
            return;
        }

        // Mark this event as processed
        self::$processedEvents[] = $eventKey;

        // Clean up old processed events (keep only last 100)
        if (count(self::$processedEvents) > 100) {
            self::$processedEvents = array_slice(self::$processedEvents, -100);
        }

        // Don't notify if user reacted to their own share
        if ($event->user->id === $event->share->user_id) {
            return;
        }

        // Check if the share owner wants to receive this type of notification
        if (!$event->share->user->wantsNotification('share_reactions')) {
            return;
        }

        // Check for duplicate notifications
        $existingNotification = $event->share->user->notifications()
            ->where('type', 'App\Notifications\ShareReacted')
            ->whereJsonContains('data->user_id', $event->user->id)
            ->whereJsonContains('data->share_id', $event->share->id)
            ->whereJsonContains('data->reaction_type', $event->reaction->type)
            ->where('created_at', '>=', now()->subMinutes(5))
            ->exists();

        if ($existingNotification) {
            return; // Skip duplicate notification
        }

        // Send notification to the share owner
        $event->share->user->notify(new ShareReacted($event->user, $event->share, $event->reaction));
    }
}
