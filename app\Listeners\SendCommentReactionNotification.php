<?php

namespace App\Listeners;

use App\Events\CommentReactionAdded;
use App\Notifications\CommentReacted;
class SendCommentReactionNotification
{
    private static $processedEvents = [];

    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(CommentReactionAdded $event): void
    {
        // Create a unique key for this event to prevent duplicate processing
        $eventKey = "comment_reaction_{$event->user->id}_{$event->comment->id}_{$event->reaction->id}";

        // Check if this event has already been processed
        if (in_array($eventKey, self::$processedEvents)) {
            return; // Skip duplicate processing
        }

        // Mark this event as processed
        self::$processedEvents[] = $eventKey;

        // Clean up old processed events (keep only last 100)
        if (count(self::$processedEvents) > 100) {
            self::$processedEvents = array_slice(self::$processedEvents, -100);
        }

        // Don't notify if user reacted to their own comment
        if ($event->user->id === $event->comment->user_id) {
            return;
        }

        // Check if the comment owner wants to receive this type of notification
        if (!$event->comment->user->wantsNotification('comment_reactions')) {
            return;
        }

        // Check for duplicate notifications
        $existingNotification = $event->comment->user->notifications()
            ->where('type', 'App\Notifications\CommentReacted')
            ->whereJsonContains('data->user_id', $event->user->id)
            ->whereJsonContains('data->comment_id', $event->comment->id)
            ->whereJsonContains('data->reaction_type', $event->reaction->type)
            ->where('created_at', '>=', now()->subMinutes(5))
            ->exists();

        if ($existingNotification) {
            return; // Skip duplicate notification
        }

        // Send notification to the comment owner
        $event->comment->user->notify(new CommentReacted($event->user, $event->comment, $event->reaction));
    }
}
