<!-- Sorting Options -->
<div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
    <div class="flex items-center justify-between">
        <h3 class="text-lg font-medium text-gray-900">Discover Users</h3>
        <div class="flex items-center space-x-2">
            <label class="text-sm text-gray-600">Sort by:</label>
            <form method="GET" action="{{ route('follow-management.following') }}" class="inline">
                <input type="hidden" name="tab" value="discover">
                @if(request('search'))
                    <input type="hidden" name="search" value="{{ request('search') }}">
                @endif
                <select name="sort" onchange="this.form.submit()" 
                        class="text-sm border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                    <option value="recent" {{ request('sort', 'recent') === 'recent' ? 'selected' : '' }}>Recently Active</option>
                    <option value="popular" {{ request('sort') === 'popular' ? 'selected' : '' }}>Most Popular</option>
                    <option value="new" {{ request('sort') === 'new' ? 'selected' : '' }}>Newest Users</option>
                    <option value="active" {{ request('sort') === 'active' ? 'selected' : '' }}>Most Active</option>
                </select>
            </form>
        </div>
    </div>
</div>

@if($items->count() > 0)
    <div class="divide-y divide-gray-200">
        @foreach($items as $user)
            <div class="p-6 flex items-center justify-between hover:bg-gray-50 transition-colors">
                <div class="flex items-center space-x-4">
                    <img src="{{ $user->avatar ? asset('storage/' . $user->avatar) : asset('images/default-avatar.png') }}" 
                         alt="{{ $user->name }}" 
                         class="w-16 h-16 rounded-full object-cover">
                    <div class="flex-1">
                        <div class="flex items-center space-x-2">
                            <h3 class="text-lg font-semibold text-gray-900">
                                <a href="{{ route('profile.user', $user) }}" 
                                   class="hover:text-blue-600 transition-colors">
                                    {{ $user->name }}
                                </a>
                            </h3>
                            @if($user->is_followed_by_current_user)
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                                    </svg>
                                    Following
                                </span>
                            @else
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-600">
                                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                    </svg>
                                    Not Following
                                </span>
                            @endif
                        </div>
                        <p class="text-gray-600">{{ $user->email }}</p>
                        @if($user->bio)
                            <p class="text-sm text-gray-500 mt-1">{{ Str::limit($user->bio, 100) }}</p>
                        @endif
                        <div class="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                            <span>{{ $user->followers()->count() }} followers</span>
                            <span>{{ $user->following()->count() }} following</span>
                            <span>{{ $user->posts()->count() }} posts</span>
                            <span>Joined {{ $user->created_at->diffForHumans() }}</span>
                            @if($user->updated_at->diffInDays() < 7)
                                <span class="text-green-600 font-medium">Recently Active</span>
                            @endif
                        </div>
                        
                        <!-- Mutual Connections -->
                        @php
                            $mutualFollowers = auth()->user()->followers()
                                ->whereIn('users.id', $user->followers()->pluck('users.id'))
                                ->limit(3)
                                ->get();
                        @endphp
                        @if($mutualFollowers->count() > 0)
                            <div class="flex items-center mt-2 text-xs text-gray-500">
                                <x-svg-icon name="Connections" class="w-3 h-3 mr-1" />
                                <span>Followed by {{ $mutualFollowers->pluck('name')->join(', ', ' and ') }}</span>
                                @if($mutualFollowers->count() < auth()->user()->followers()->whereIn('users.id', $user->followers()->pluck('users.id'))->count())
                                    <span> and {{ auth()->user()->followers()->whereIn('users.id', $user->followers()->pluck('users.id'))->count() - $mutualFollowers->count() }} others</span>
                                @endif
                            </div>
                        @endif
                    </div>
                </div>
                
                <div class="flex items-center space-x-3">
                    <livewire:user-follower :user="$user" :key="'discover-user-' . $user->id" />
                    <a href="{{ route('profile.user', $user) }}" 
                       class="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors">
                        View Profile
                    </a>
                </div>
            </div>
        @endforeach
    </div>

    <!-- Pagination -->
    <div class="px-6 py-4 border-t border-gray-200">
        {{ $items->links() }}
    </div>
@else
    <div class="p-12 text-center">
        <x-svg-icon name="Discover_Users" class="w-16 h-16 text-gray-300 mx-auto mb-4" />
        <h3 class="text-lg font-medium text-gray-900 mb-2">
            @if(request('search'))
                No users found
            @else
                No users to discover
            @endif
        </h3>
        <p class="text-gray-600">
            @if(request('search'))
                Try adjusting your search terms or filters.
            @else
                All users on the platform are already being followed by you.
            @endif
        </p>
        @if(request('search'))
            <div class="mt-4">
                <a href="{{ route('follow-management.following', ['tab' => 'discover']) }}" 
                   class="text-blue-600 hover:text-blue-800 font-medium">
                    View all users
                </a>
            </div>
        @endif
    </div>
@endif
