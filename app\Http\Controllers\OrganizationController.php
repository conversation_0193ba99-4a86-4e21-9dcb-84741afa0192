<?php

namespace App\Http\Controllers;

use App\Models\Organization;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;

class OrganizationController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $organizations = Organization::with(['creator', 'activeMembers'])
            ->where('status', 'active')
            ->withCount('activeMembers')
            ->orderBy('name')
            ->paginate(12);

        return view('organizations.index', compact('organizations'));
    }

    /**
     * Display user's organizations
     */
    public function my()
    {
        $user = auth()->user();

        $myOrganizations = $user->organizations()
            ->with(['creator'])
            ->withCount('activeMembers')
            ->wherePivot('status', 'active')
            ->orderBy('name')
            ->get();

        $pendingInvitations = $user->organizations()
            ->with(['creator'])
            ->withCount('activeMembers')
            ->wherePivot('status', 'pending')
            ->orderBy('name')
            ->get();

        return view('organizations.my', compact('myOrganizations', 'pendingInvitations'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('organizations.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:organizations,name',
            'description' => 'required|string|max:1000',
            'email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:20',
            'website' => 'nullable|url|max:255',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'cover_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:5120',
        ]);

        $validated['slug'] = Str::slug($validated['name']);
        $validated['created_by'] = auth()->id();
        $validated['status'] = auth()->user()->isAdmin() ? 'active' : 'pending';

        // Handle file uploads
        if ($request->hasFile('logo')) {
            $validated['logo'] = $request->file('logo')->store('organizations/logos', 'public');
        }

        if ($request->hasFile('cover_image')) {
            $validated['cover_image'] = $request->file('cover_image')->store('organizations/covers', 'public');
        }

        $organization = Organization::create($validated);

        // Add creator as president
        $organization->members()->attach(auth()->id(), [
            'role' => 'president',
            'status' => 'active',
            'joined_at' => now(),
        ]);

        return redirect()->route('organizations.show', $organization)
            ->with('success', 'Organization created successfully!');
    }

    /**
     * Display the specified resource.
     */
    public function show(Organization $organization)
    {
        // If in page mode, redirect to page view
        if ($organization->is_page_mode) {
            return $this->page($organization);
        }

        $organization->load([
            'creator',
            'activeMembers' => function ($query) {
                $query->orderBy('organization_members.role', 'desc')
                      ->orderBy('organization_members.joined_at');
            },
            'posts' => function ($query) {
                $query->published()
                      ->approved()
                      ->with(['user', 'likes', 'comments.user', 'comments.reactions', 'comments.replies.user', 'comments.replies.reactions', 'fileAttachments'])
                      ->latest('published_at')
                      ->limit(10);
            }
        ]);

        $userMembership = null;
        if (auth()->check()) {
            $userMembership = $organization->members()
                ->where('user_id', auth()->id())
                ->first();
        }

        return view('organizations.show', compact('organization', 'userMembership'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Organization $organization)
    {
        // Check if user can edit this organization
        if (!$this->canManageOrganization($organization)) {
            abort(403, 'Unauthorized to edit this organization.');
        }

        return view('organizations.edit', compact('organization'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Organization $organization)
    {
        // Check if user can edit this organization
        if (!$this->canManageOrganization($organization)) {
            abort(403, 'Unauthorized to update this organization.');
        }

        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255', Rule::unique('organizations')->ignore($organization)],
            'description' => 'required|string|max:1000',
            'email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:20',
            'website' => 'nullable|url|max:255',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'cover_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:5120',
        ]);

        $validated['slug'] = Str::slug($validated['name']);

        // Handle file uploads
        if ($request->hasFile('logo')) {
            $validated['logo'] = $request->file('logo')->store('organizations/logos', 'public');
        }

        if ($request->hasFile('cover_image')) {
            $validated['cover_image'] = $request->file('cover_image')->store('organizations/covers', 'public');
        }

        $organization->update($validated);

        return redirect()->route('organizations.show', $organization)
            ->with('success', 'Organization updated successfully!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Organization $organization)
    {
        // Only admins or organization creator can delete
        if (!auth()->user()->isAdmin() && $organization->created_by !== auth()->id()) {
            abort(403, 'Unauthorized to delete this organization.');
        }

        $organization->delete();

        return redirect()->route('organizations.index')
            ->with('success', 'Organization deleted successfully!');
    }

    /**
     * Join an organization
     */
    public function join(Organization $organization)
    {
        $user = auth()->user();

        // Check if already a member
        if ($organization->members()->where('user_id', $user->id)->exists()) {
            return back()->with('error', 'You are already a member of this organization.');
        }

        // Add as pending member
        $organization->members()->attach($user->id, [
            'role' => 'member',
            'status' => 'pending',
        ]);

        return back()->with('success', 'Join request sent! Waiting for approval.');
    }

    /**
     * Leave an organization
     */
    public function leave(Organization $organization)
    {
        $user = auth()->user();

        $membership = $organization->members()->where('user_id', $user->id)->first();

        if (!$membership) {
            return back()->with('error', 'You are not a member of this organization.');
        }

        // Presidents cannot leave unless they transfer leadership
        if ($membership->pivot->role === 'president') {
            return back()->with('error', 'Presidents must transfer leadership before leaving.');
        }

        $organization->members()->detach($user->id);

        return back()->with('success', 'You have left the organization.');
    }

    /**
     * Follow an organization (page mode)
     */
    public function follow(Organization $organization)
    {
        $user = auth()->user();

        // Check if already following
        if ($organization->isFollowedBy($user)) {
            return back()->with('error', 'You are already following this organization.');
        }

        $organization->followers()->attach($user->id);

        return back()->with('success', 'You are now following this organization!');
    }

    /**
     * Unfollow an organization (page mode)
     */
    public function unfollow(Organization $organization)
    {
        $user = auth()->user();

        // Check if following
        if (!$organization->isFollowedBy($user)) {
            return back()->with('error', 'You are not following this organization.');
        }

        $organization->followers()->detach($user->id);

        return back()->with('success', 'You have unfollowed this organization.');
    }

    /**
     * Toggle page mode for organization
     */
    public function togglePageMode(Organization $organization)
    {
        // Check permissions
        if (!$this->canManageOrganization($organization)) {
            abort(403, 'You do not have permission to modify this organization.');
        }

        $organization->update([
            'is_page_mode' => !$organization->is_page_mode
        ]);

        $mode = $organization->is_page_mode ? 'page' : 'organization';
        return back()->with('success', "Organization switched to {$mode} mode successfully!");
    }

    /**
     * Show organization followers (page mode)
     */
    public function followers(Organization $organization)
    {
        if (!$organization->is_page_mode) {
            return redirect()->route('organizations.show', $organization);
        }

        $followers = $organization->followers()
            ->orderBy('organization_followers.created_at', 'desc')
            ->paginate(20);

        return view('organizations.followers', compact('organization', 'followers'));
    }

    /**
     * Show organization page in page mode
     */
    public function page(Organization $organization)
    {
        // Redirect to regular view if not in page mode
        if (!$organization->is_page_mode) {
            return redirect()->route('organizations.show', $organization);
        }

        $organization->load([
            'creator',
            'officers',
            'posts' => function ($query) {
                $query->published()
                      ->approved()
                      ->with(['user', 'likes', 'comments', 'fileAttachments'])
                      ->latest('published_at')
                      ->limit(10);
            }
        ]);

        $userFollowing = null;
        $userMembership = null;

        if (auth()->check()) {
            $userFollowing = $organization->isFollowedBy(auth()->user());
            $userMembership = $organization->members()
                ->where('user_id', auth()->id())
                ->first();
        }

        $followersCount = $organization->followers()->count();

        return view('organizations.page', compact(
            'organization',
            'userFollowing',
            'userMembership',
            'followersCount'
        ));
    }

    /**
     * Check if user can manage organization
     */
    private function canManageOrganization(Organization $organization): bool
    {
        $user = auth()->user();

        // Admins can manage any organization
        if ($user->isAdmin()) {
            return true;
        }

        // Organization creator can manage
        if ($organization->created_by === $user->id) {
            return true;
        }

        // Officers and presidents can manage
        $membership = $organization->members()->where('user_id', $user->id)->first();
        if ($membership && in_array($membership->pivot->role, ['officer', 'president'])) {
            return true;
        }

        return false;
    }
}
