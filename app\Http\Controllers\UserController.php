<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class UserController extends Controller
{
    /**
     * Search users for header search functionality
     */
    public function search(Request $request)
    {
        $query = $request->get('q', '');
        
        if (empty($query) || strlen($query) < 1) {
            return response()->json([
                'success' => true,
                'users' => []
            ]);
        }

        // Search users by name, email, or student_id
        $users = User::where(function($q) use ($query) {
                $q->where('name', 'like', "%{$query}%")
                  ->orWhere('email', 'like', "%{$query}%")
                  ->orWhere('student_id', 'like', "%{$query}%")
                  ->orWhere('bio', 'like', "%{$query}%");
            })
            ->where('id', '!=', auth()->id()) // Exclude current user
            ->select('id', 'name', 'email', 'avatar', 'role', 'student_id')
            ->limit(10) // Limit results for performance
            ->get()
            ->map(function($user) {
                return [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'student_id' => $user->student_id,
                    'role' => $user->role,
                    'avatar_url' => $user->avatar 
                        ? Storage::disk('public')->url($user->avatar)
                        : 'https://ui-avatars.com/api/?name=' . urlencode($user->name) . '&color=7BC74D&background=EEEEEE',
                    'profile_url' => route('profile.user', $user->id)
                ];
            });

        return response()->json([
            'success' => true,
            'users' => $users
        ]);
    }
}
