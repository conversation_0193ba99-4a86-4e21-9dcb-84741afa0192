<?php

namespace App\Listeners;

use App\Events\ShareCommentAdded;
use App\Notifications\ShareCommented;
use App\Notifications\CommentReplied;

class SendShareCommentNotification
{
    private static $processedEvents = [];

    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(ShareCommentAdded $event): void
    {
        // Create a unique key for this event to prevent duplicates
        $eventKey = "share_comment_{$event->user->id}_{$event->share->id}_{$event->comment->id}_" . now()->timestamp;

        // Check if we've already processed this event
        if (in_array($eventKey, self::$processedEvents)) {
            return;
        }

        // Mark this event as processed
        self::$processedEvents[] = $eventKey;

        // Clean up old processed events (keep only last 100)
        if (count(self::$processedEvents) > 100) {
            self::$processedEvents = array_slice(self::$processedEvents, -100);
        }

        // If this is a reply to another comment
        if ($event->comment->parent_id) {
            $parentComment = $event->comment->parent;
            
            // Don't notify if user replied to their own comment
            if ($event->user->id !== $parentComment->user_id) {
                // Check if the parent comment owner wants to receive this type of notification
                if ($parentComment->user->wantsNotification('comment_replies')) {
                    $parentComment->user->notify(new CommentReplied($event->user, $parentComment, $event->comment));
                }
            }
        } else {
            // This is a top-level comment on the share
            // Don't notify if user commented on their own share
            if ($event->user->id === $event->share->user_id) {
                return;
            }

            // Check if the share owner wants to receive this type of notification
            if (!$event->share->user->wantsNotification('share_comments')) {
                return;
            }

            // Check for duplicate notifications
            $existingNotification = $event->share->user->notifications()
                ->where('type', 'App\Notifications\ShareCommented')
                ->whereJsonContains('data->user_id', $event->user->id)
                ->whereJsonContains('data->share_id', $event->share->id)
                ->whereJsonContains('data->comment_id', $event->comment->id)
                ->where('created_at', '>=', now()->subMinutes(5))
                ->exists();

            if ($existingNotification) {
                return; // Skip duplicate notification
            }

            // Send notification to the share owner
            $event->share->user->notify(new ShareCommented($event->user, $event->share, $event->comment));
        }
    }
}
