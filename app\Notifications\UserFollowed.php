<?php

namespace App\Notifications;

use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Messages\BroadcastMessage;
use Illuminate\Notifications\Notification;

class UserFollowed extends Notification implements ShouldQueue
{
    use Queueable;

    public $follower;

    /**
     * Create a new notification instance.
     */
    public function __construct(User $follower)
    {
        $this->follower = $follower;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['database', 'broadcast'];
    }

    /**
     * Get the broadcastable representation of the notification.
     */
    public function toBroadcast(object $notifiable): BroadcastMessage
    {
        return new BroadcastMessage([
            'id' => $this->id,
            'type' => 'user_followed',
            'user_id' => $this->follower->id,
            'user_name' => $this->follower->name,
            'user_avatar' => $this->follower->getNotificationAvatarUrl(),
            'message' => "{$this->follower->name} started following you",
            'url' => route('profile.user', $this->follower),
            'created_at' => now()->toISOString(),
        ]);
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'type' => 'user_followed',
            'user_id' => $this->follower->id,
            'user_name' => $this->follower->name,
            'user_avatar' => $this->follower->getNotificationAvatarUrl(),
            'message' => "{$this->follower->name} started following you",
            'url' => route('profile.user', $this->follower),
        ];
    }
}
