<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class NotificationPreferencesController extends Controller
{
    /**
     * Show notification preferences page
     */
    public function show()
    {
        $user = Auth::user();
        $preferences = $user->getNotificationPreferences();

        return view('profile.notification-preferences', compact('preferences'));
    }

    /**
     * Update notification preferences
     */
    public function update(Request $request)
    {
        $user = Auth::user();

        $validated = $request->validate([
            'notifications_enabled' => 'boolean',
            'post_reactions' => 'boolean',
            'post_comments' => 'boolean',
            'post_shares' => 'boolean',
            'comment_reactions' => 'boolean',
            'comment_replies' => 'boolean',
            'user_follows' => 'boolean',
            'organization_posts' => 'boolean',
            'organization_approvals' => 'boolean',
            'group_posts' => 'boolean',
            'group_memberships' => 'boolean',
            'group_membership_requests' => 'boolean',
            'group_post_approvals' => 'boolean',
            'scholarship_updates' => 'boolean',
            'admin_notifications' => 'boolean',
        ]);

        // Extract notifications_enabled separately
        $notificationsEnabled = $validated['notifications_enabled'] ?? true;
        unset($validated['notifications_enabled']);

        // Update user preferences
        $user->update([
            'notifications_enabled' => $notificationsEnabled,
            'notification_preferences' => $validated,
        ]);

        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Notification preferences updated successfully!',
                'preferences' => $user->getNotificationPreferences(),
            ]);
        }

        return back()->with('success', 'Notification preferences updated successfully!');
    }
}
