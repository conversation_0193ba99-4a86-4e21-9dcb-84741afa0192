<?php

use App\Models\User;
use App\Models\Group;
use App\Models\Post;
use App\Models\Organization;

test('group post card displays Facebook-style header with both group and user avatars', function () {
    // Create a user and group
    $user = User::factory()->create([
        'name' => '<PERSON>'
    ]);
    $organization = Organization::factory()->create();
    $group = Group::factory()->create([
        'created_by' => $user->id,
        'organization_id' => $organization->id,
        'visibility' => 'public',
        'name' => 'Test Group'
    ]);

    // Add user as member
    $group->members()->attach($user->id, [
        'role' => 'admin',
        'status' => 'active',
        'joined_at' => now()
    ]);

    // Create a post in the group
    $post = Post::factory()->create([
        'user_id' => $user->id,
        'group_id' => $group->id,
        'title' => 'Test Group Post',
        'content' => 'This is a test post in the group',
        'status' => 'published',
        'approval_status' => 'approved',
        'published_at' => now()
    ]);

    // Visit the group page
    $response = $this->actingAs($user)->get("/groups/{$group->slug}");

    $response->assertStatus(200);

    // Check that the group name appears in the post header
    $response->assertSee($group->name);

    // Check that the user name appears below the group name
    $response->assertSee($user->name);

    // Check that the post title and content are displayed
    $response->assertSee($post->title);
    $response->assertSee($post->content);

    // Check that both avatars are present in the HTML structure
    // Group avatar should be larger (h-12 w-12)
    $response->assertSee('h-12 w-12 rounded-full');

    // User avatar should be smaller (h-8 w-8) with border
    $response->assertSee('h-8 w-8 rounded-full border-2 border-white');
});

test('private group post shows privacy indicator', function () {
    // Create a user and private group
    $user = User::factory()->create();
    $group = Group::factory()->create([
        'created_by' => $user->id,
        'visibility' => 'private',
        'name' => 'Private Test Group'
    ]);

    // Add user as member
    $group->members()->attach($user->id, [
        'role' => 'admin',
        'status' => 'active',
        'joined_at' => now()
    ]);

    // Create a post in the private group
    $post = Post::factory()->create([
        'user_id' => $user->id,
        'group_id' => $group->id,
        'title' => 'Private Group Post',
        'content' => 'This is a test post in the private group',
        'status' => 'published',
        'approval_status' => 'approved',
        'published_at' => now()
    ]);

    // Visit the group page
    $response = $this->actingAs($user)->get("/groups/{$group->slug}");

    $response->assertStatus(200);
    
    // Check that the group name appears
    $response->assertSee($group->name);
    
    // Check that the user name appears
    $response->assertSee($user->name);
    
    // The privacy indicator (lock icon) should be present in the HTML
    // We can't easily test for SVG content, but we can check the structure
    $response->assertSee('Private Test Group');
});

test('group post card shows correct timestamp', function () {
    // Create a user and group
    $user = User::factory()->create();
    $group = Group::factory()->create([
        'created_by' => $user->id,
        'visibility' => 'public'
    ]);

    // Add user as member
    $group->members()->attach($user->id, [
        'role' => 'admin',
        'status' => 'active',
        'joined_at' => now()
    ]);

    // Create a post with a specific timestamp
    $publishedAt = now()->subHours(2);
    $post = Post::factory()->create([
        'user_id' => $user->id,
        'group_id' => $group->id,
        'status' => 'published',
        'approval_status' => 'approved',
        'published_at' => $publishedAt
    ]);

    // Visit the group page
    $response = $this->actingAs($user)->get("/groups/{$group->slug}");

    $response->assertStatus(200);

    // Check that a relative timestamp is shown (like "2 hours ago")
    $response->assertSee('hours ago');
});

test('group post displays both group and user profile pictures', function () {
    // Create a user with a specific name for avatar generation
    $user = User::factory()->create([
        'name' => 'Jane Smith'
    ]);

    // Create a group with a specific name
    $group = Group::factory()->create([
        'created_by' => $user->id,
        'visibility' => 'public',
        'name' => 'Photography Club'
    ]);

    // Add user as member
    $group->members()->attach($user->id, [
        'role' => 'admin',
        'status' => 'active',
        'joined_at' => now()
    ]);

    // Create a post in the group
    $post = Post::factory()->create([
        'user_id' => $user->id,
        'group_id' => $group->id,
        'title' => 'New Photo Contest',
        'content' => 'Submit your best photos for our monthly contest!',
        'status' => 'published',
        'approval_status' => 'approved',
        'published_at' => now()
    ]);

    // Visit the group page
    $response = $this->actingAs($user)->get("/groups/{$group->slug}");

    $response->assertStatus(200);

    // Check that both the group and user names are present
    $response->assertSee('Photography Club');
    $response->assertSee('Jane Smith');

    // Check that the HTML contains the dual avatar structure
    $content = $response->getContent();

    // Should contain group avatar (larger)
    expect($content)->toContain('h-12 w-12 rounded-full');

    // Should contain user avatar (smaller with border)
    expect($content)->toContain('h-8 w-8 rounded-full border-2 border-white');

    // Should contain the overlapping structure
    expect($content)->toContain('relative -ml-2');
});
