<?php

namespace App\Events;

use App\Models\User;
use App\Models\Share;
use App\Models\Comment;

use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ShareCommentAdded
{
    use Dispatchable, SerializesModels;

    public $user;
    public $share;
    public $comment;

    /**
     * Create a new event instance.
     */
    public function __construct(User $user, Share $share, Comment $comment)
    {
        $this->user = $user;
        $this->share = $share;
        $this->comment = $comment;
    }
}
