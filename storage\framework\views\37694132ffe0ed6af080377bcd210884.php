<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'name' => '',
    'class' => 'w-5 h-5',
    'fill' => 'currentColor',
    'stroke' => 'none'
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'name' => '',
    'class' => 'w-5 h-5',
    'fill' => 'currentColor',
    'stroke' => 'none'
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars, $__key, $__value); ?>

<?php
    $iconPath = public_path("assets/SVG_Icons/{$name}.svg");
    $iconExists = file_exists($iconPath);

    if ($iconExists) {
        $iconContent = file_get_contents($iconPath);
        // Remove XML declaration and comments for cleaner output
        $iconContent = preg_replace('/<\?xml[^>]*\?>/', '', $iconContent);
        $iconContent = preg_replace('/<!--.*?-->/s', '', $iconContent);
        $iconContent = trim($iconContent);

        // Extract viewBox from the original SVG
        preg_match('/viewBox="([^"]*)"/', $iconContent, $viewBoxMatches);
        $viewBox = $viewBoxMatches[1] ?? '0 0 24 24';

        // Extract the inner content (paths, circles, etc.)
        preg_match('/<svg[^>]*>(.*?)<\/svg>/s', $iconContent, $contentMatches);
        $innerContent = $contentMatches[1] ?? '';

        // Clean up the inner content and replace hardcoded colors with currentColor if needed
        $innerContent = trim($innerContent);
        // Replace hardcoded black fills with currentColor for better theming
        $innerContent = str_replace('fill="#000000"', 'fill="currentColor"', $innerContent);
        $innerContent = str_replace('fill="#000"', 'fill="currentColor"', $innerContent);
    }
?>

<?php if($iconExists): ?>
    <svg <?php echo e($attributes->merge(['class' => $class])); ?> 
         viewBox="<?php echo e($viewBox); ?>" 
         fill="<?php echo e($fill); ?>" 
         stroke="<?php echo e($stroke); ?>"
         xmlns="http://www.w3.org/2000/svg">
        <?php echo $innerContent; ?>

    </svg>
<?php else: ?>
    <!-- Fallback icon if SVG not found -->
    <svg <?php echo e($attributes->merge(['class' => $class])); ?> 
         fill="none" 
         stroke="currentColor" 
         viewBox="0 0 24 24" 
         xmlns="http://www.w3.org/2000/svg">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
<?php endif; ?>
<?php /**PATH C:\xampp\htdocs\LARAVEL_PROJECTS\lara_unilink\resources\views/components/svg-icon.blade.php ENDPATH**/ ?>