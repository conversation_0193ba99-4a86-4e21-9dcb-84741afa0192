<?php

namespace App\Notifications;

use App\Models\User;
use App\Models\Comment;
use App\Models\Reaction;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\BroadcastMessage;
use Illuminate\Notifications\Notification;

class CommentReacted extends Notification implements ShouldQueue
{
    use Queueable;

    public $user;
    public $comment;
    public $reaction;

    /**
     * Create a new notification instance.
     */
    public function __construct(User $user, Comment $comment, Reaction $reaction)
    {
        $this->user = $user;
        $this->comment = $comment;
        $this->reaction = $reaction;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['database', 'broadcast'];
    }

    /**
     * Get the broadcastable representation of the notification.
     */
    public function toBroadcast(object $notifiable): BroadcastMessage
    {
        return new BroadcastMessage([
            'id' => $this->id,
            'type' => 'comment_reacted',
            'user_id' => $this->user->id,
            'user_name' => $this->user->name,
            'user_avatar' => $this->user->getNotificationAvatarUrl(),
            'comment_id' => $this->comment->id,
            'comment_content' => $this->comment->content,
            'post_id' => $this->comment->commentable_id,
            'post_title' => $this->comment->commentable->title ?? 'a post',
            'reaction_type' => $this->reaction->type,
            'reaction_emoji' => $this->getReactionEmoji($this->reaction->type),
            'message' => $this->getMessage(),
            'url' => $this->getCommentUrl(),
            'created_at' => now()->toISOString(),
        ]);
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'type' => 'comment_reacted',
            'user_id' => $this->user->id,
            'user_name' => $this->user->name,
            'user_avatar' => $this->user->getNotificationAvatarUrl(),
            'comment_id' => $this->comment->id,
            'comment_content' => $this->comment->content,
            'post_id' => $this->comment->commentable_id,
            'post_title' => $this->comment->commentable->title ?? 'a post',
            'reaction_type' => $this->reaction->type,
            'reaction_emoji' => $this->getReactionEmoji($this->reaction->type),
            'message' => $this->getMessage(),
            'url' => $this->getCommentUrl(),
        ];
    }

    /**
     * Get the notification message
     */
    private function getMessage(): string
    {
        $reactionLabel = $this->getReactionLabel($this->reaction->type);
        return "{$this->user->name} reacted {$reactionLabel} to your comment";
    }

    /**
     * Get the comment URL
     */
    private function getCommentUrl(): string
    {
        $post = $this->comment->commentable;
        
        if ($post->group_id) {
            return route('groups.show', $post->group->slug) . '#comment-' . $this->comment->id;
        } elseif ($post->organization_id) {
            return route('organizations.show', $post->organization->slug) . '#comment-' . $this->comment->id;
        } else {
            return route('profile.user', $post->user) . '#comment-' . $this->comment->id;
        }
    }

    /**
     * Get reaction emoji
     */
    private function getReactionEmoji(string $type): string
    {
        $emojis = [
            'like' => '👍',
            'love' => '❤️',
            'haha' => '😂',
            'wow' => '😮',
            'sad' => '😢',
            'angry' => '😠',
        ];

        return $emojis[$type] ?? '👍';
    }

    /**
     * Get reaction label
     */
    private function getReactionLabel(string $type): string
    {
        $labels = [
            'like' => 'with a like',
            'love' => 'with love',
            'haha' => 'with laughter',
            'wow' => 'with wow',
            'sad' => 'with sadness',
            'angry' => 'with anger',
        ];

        return $labels[$type] ?? 'with a like';
    }
}
