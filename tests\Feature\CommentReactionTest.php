<?php

use App\Models\User;
use App\Models\Post;
use App\Models\Comment;
use App\Models\Reaction;

test('comment includes user reaction data when created', function () {
    // Create a user and post
    $user = User::factory()->create();
    $post = Post::factory()->create(['user_id' => $user->id]);
    
    // Create a comment
    $response = $this->actingAs($user)->withoutMiddleware()->postJson("/posts/{$post->id}/comments", [
        'content' => 'Test comment'
    ]);
    
    $response->assertStatus(200);
    $response->assertJson(['success' => true]);
    
    $comment = $response->json('comment');
    
    // Check that user reaction data is included
    expect($comment)->toHave<PERSON>ey('user_reaction');
    expect($comment['user_reaction'])->toBeNull(); // No reaction initially
    expect($comment)->toHaveKey('is_liked_by_user');
    expect($comment['is_liked_by_user'])->toBeFalse();
    expect($comment)->toHaveKey('likes_count');
    expect($comment['likes_count'])->toBe(0);
});

test('comment includes user reaction data when fetched', function () {
    // Create a user and post
    $user = User::factory()->create();
    $post = Post::factory()->create(['user_id' => $user->id]);
    
    // Create a comment
    $comment = Comment::create([
        'content' => 'Test comment',
        'user_id' => $user->id,
        'commentable_type' => Post::class,
        'commentable_id' => $post->id,
    ]);
    
    // Add a reaction to the comment
    Reaction::create([
        'user_id' => $user->id,
        'reactable_type' => Comment::class,
        'reactable_id' => $comment->id,
        'type' => 'love'
    ]);
    
    // Fetch comments
    $response = $this->actingAs($user)->getJson("/posts/{$post->id}/comments");
    
    $response->assertStatus(200);
    $response->assertJson(['success' => true]);
    
    $comments = $response->json('comments');
    expect($comments)->toHaveCount(1);
    
    $fetchedComment = $comments[0];
    
    // Check that user reaction data is included
    expect($fetchedComment)->toHaveKey('user_reaction');
    expect($fetchedComment['user_reaction'])->not->toBeNull();
    expect($fetchedComment['user_reaction']['type'])->toBe('love');
    expect($fetchedComment)->toHaveKey('is_liked_by_user');
    expect($fetchedComment)->toHaveKey('likes_count');
});

test('reply includes user reaction data when created', function () {
    // Create a user and post
    $user = User::factory()->create();
    $post = Post::factory()->create(['user_id' => $user->id]);
    
    // Create a parent comment
    $parentComment = Comment::create([
        'content' => 'Parent comment',
        'user_id' => $user->id,
        'commentable_type' => Post::class,
        'commentable_id' => $post->id,
    ]);
    
    // Create a reply
    $response = $this->actingAs($user)->withoutMiddleware()->postJson("/posts/{$post->id}/comments", [
        'content' => 'Test reply',
        'parent_id' => $parentComment->id
    ]);
    
    $response->assertStatus(200);
    $response->assertJson(['success' => true]);
    
    $reply = $response->json('comment');
    
    // Check that user reaction data is included
    expect($reply)->toHaveKey('user_reaction');
    expect($reply['user_reaction'])->toBeNull(); // No reaction initially
    expect($reply)->toHaveKey('is_liked_by_user');
    expect($reply['is_liked_by_user'])->toBeFalse();
    expect($reply)->toHaveKey('likes_count');
    expect($reply['likes_count'])->toBe(0);
});

test('comment includes reaction counts when created', function () {
    // Create a user and post
    $user = User::factory()->create();
    $post = Post::factory()->create(['user_id' => $user->id]);

    // Create a comment
    $response = $this->actingAs($user)->withoutMiddleware()->postJson("/posts/{$post->id}/comments", [
        'content' => 'Test comment'
    ]);

    $response->assertStatus(200);
    $response->assertJson(['success' => true]);

    $comment = $response->json('comment');

    // Check that reaction count data is included
    expect($comment)->toHaveKey('reactions_count');
    expect($comment['reactions_count'])->toBe(0);
    expect($comment)->toHaveKey('reaction_counts');
    expect($comment['reaction_counts'])->toBeArray();
    expect($comment['reaction_counts'])->toBeEmpty();
});

test('comment includes reaction counts when fetched with reactions', function () {
    // Create users and post
    $user1 = User::factory()->create();
    $user2 = User::factory()->create();
    $user3 = User::factory()->create();
    $post = Post::factory()->create(['user_id' => $user1->id]);

    // Create a comment
    $comment = Comment::create([
        'content' => 'Test comment',
        'user_id' => $user1->id,
        'commentable_type' => Post::class,
        'commentable_id' => $post->id,
    ]);

    // Add different reactions to the comment
    Reaction::create([
        'user_id' => $user1->id,
        'reactable_type' => Comment::class,
        'reactable_id' => $comment->id,
        'type' => 'like'
    ]);

    Reaction::create([
        'user_id' => $user2->id,
        'reactable_type' => Comment::class,
        'reactable_id' => $comment->id,
        'type' => 'love'
    ]);

    Reaction::create([
        'user_id' => $user3->id,
        'reactable_type' => Comment::class,
        'reactable_id' => $comment->id,
        'type' => 'like'
    ]);

    // Fetch comments
    $response = $this->actingAs($user1)->getJson("/posts/{$post->id}/comments");

    $response->assertStatus(200);
    $response->assertJson(['success' => true]);

    $comments = $response->json('comments');
    expect($comments)->toHaveCount(1);

    $fetchedComment = $comments[0];

    // Check that reaction count data is included
    expect($fetchedComment)->toHaveKey('reactions_count');
    expect($fetchedComment['reactions_count'])->toBe(3);
    expect($fetchedComment)->toHaveKey('reaction_counts');
    expect($fetchedComment['reaction_counts'])->toBeArray();
    expect($fetchedComment['reaction_counts'])->toHaveKey('like');
    expect($fetchedComment['reaction_counts']['like'])->toBe(2);
    expect($fetchedComment['reaction_counts'])->toHaveKey('love');
    expect($fetchedComment['reaction_counts']['love'])->toBe(1);
});

test('comment reaction counts are visible to all users', function () {
    // Create users and post
    $user1 = User::factory()->create();
    $user2 = User::factory()->create();
    $user3 = User::factory()->create(); // This user will view but not react
    $post = Post::factory()->create(['user_id' => $user1->id]);

    // Create a comment
    $comment = Comment::create([
        'content' => 'Test comment',
        'user_id' => $user1->id,
        'commentable_type' => Post::class,
        'commentable_id' => $post->id,
    ]);

    // Add reactions from user1 and user2
    Reaction::create([
        'user_id' => $user1->id,
        'reactable_type' => Comment::class,
        'reactable_id' => $comment->id,
        'type' => 'like'
    ]);

    Reaction::create([
        'user_id' => $user2->id,
        'reactable_type' => Comment::class,
        'reactable_id' => $comment->id,
        'type' => 'love'
    ]);

    // Fetch comments as user3 (who hasn't reacted)
    $response = $this->actingAs($user3)->getJson("/posts/{$post->id}/comments");

    $response->assertStatus(200);
    $response->assertJson(['success' => true]);

    $comments = $response->json('comments');
    expect($comments)->toHaveCount(1);

    $fetchedComment = $comments[0];

    // User3 should still see the reaction counts even though they haven't reacted
    expect($fetchedComment)->toHaveKey('reactions_count');
    expect($fetchedComment['reactions_count'])->toBe(2);
    expect($fetchedComment)->toHaveKey('reaction_counts');
    expect($fetchedComment['reaction_counts'])->toBeArray();
    expect($fetchedComment['reaction_counts'])->toHaveKey('like');
    expect($fetchedComment['reaction_counts']['like'])->toBe(1);
    expect($fetchedComment['reaction_counts'])->toHaveKey('love');
    expect($fetchedComment['reaction_counts']['love'])->toBe(1);

    // User3's own reaction data should be null/false
    expect($fetchedComment['user_reaction'])->toBeNull();
    expect($fetchedComment['is_liked_by_user'])->toBeFalse();
});
