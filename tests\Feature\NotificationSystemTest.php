<?php

use App\Models\User;
use App\Models\Post;
use App\Models\Comment;
use App\Models\Reaction;
use App\Events\PostReactionAdded;
use App\Events\PostCommentAdded;
use App\Notifications\PostReacted;
use App\Notifications\PostCommented;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Notification;

test('user receives notification when post is reacted to', function () {
    Notification::fake();

    // Create users
    $postOwner = User::factory()->create();
    $reactor = User::factory()->create();

    // Create a post
    $post = Post::factory()->create(['user_id' => $postOwner->id]);

    // Create a reaction
    $reaction = Reaction::factory()->create([
        'reactable_type' => Post::class,
        'reactable_id' => $post->id,
        'user_id' => $reactor->id,
        'type' => 'like'
    ]);

    // Fire the event
    event(new PostReactionAdded($reactor, $post, $reaction));

    // Assert notification was sent
    Notification::assertSentTo($postOwner, PostReacted::class);
});

test('user receives notification when post is commented on', function () {
    Notification::fake();

    // Create users
    $postOwner = User::factory()->create();
    $commenter = User::factory()->create();

    // Create a post
    $post = Post::factory()->create(['user_id' => $postOwner->id]);

    // Create a comment
    $comment = Comment::factory()->create([
        'commentable_type' => Post::class,
        'commentable_id' => $post->id,
        'user_id' => $commenter->id
    ]);

    // Fire the event
    event(new PostCommentAdded($commenter, $post, $comment));

    // Assert notification was sent
    Notification::assertSentTo($postOwner, PostCommented::class);
});

test('notification api endpoints work correctly', function () {
    $user = User::factory()->create();

    // Test notifications index
    $response = $this->actingAs($user)->get('/notifications');
    $response->assertStatus(200);

    // Test unread count
    $response = $this->actingAs($user)->get('/notifications/unread-count');
    $response->assertStatus(200);
    $response->assertJsonStructure(['unread_count']);
});
