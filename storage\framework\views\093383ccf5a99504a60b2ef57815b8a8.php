<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'name' => 'files',
    'accept' => '*',
    'multiple' => false,
    'maxSize' => '10MB',
    'allowedTypes' => null,
    'title' => 'Upload Files',
    'description' => 'Drag and drop files here or click to browse'
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'name' => 'files',
    'accept' => '*',
    'multiple' => false,
    'maxSize' => '10MB',
    'allowedTypes' => null,
    'title' => 'Upload Files',
    'description' => 'Drag and drop files here or click to browse'
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars, $__key, $__value); ?>

<div class="file-upload-zone">
    <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors relative"
         ondrop="handleDrop(event, '<?php echo e($name); ?>')" 
         ondragover="handleDragOver(event)" 
         ondragenter="handleDragEnter(event)" 
         ondragleave="handleDragLeave(event)">
        
        <input type="file" 
               name="<?php echo e($name); ?><?php echo e($multiple ? '[]' : ''); ?>" 
               id="<?php echo e($name); ?>" 
               <?php echo e($multiple ? 'multiple' : ''); ?>

               accept="<?php echo e($accept); ?>"
               class="hidden" 
               onchange="handleFileSelect(this, '<?php echo e($name); ?>')">
        
        <label for="<?php echo e($name); ?>" class="cursor-pointer block">
            <div class="upload-icon">
                <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                    <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                </svg>
            </div>
            <div class="mt-2">
                <p class="text-sm text-gray-600 font-medium"><?php echo e($title); ?></p>
                <p class="text-xs text-gray-500"><?php echo e($description); ?></p>
                <?php if($allowedTypes): ?>
                    <p class="text-xs text-gray-500 mt-1"><?php echo e(strtoupper(implode(', ', $allowedTypes))); ?> up to <?php echo e($maxSize); ?></p>
                <?php else: ?>
                    <p class="text-xs text-gray-500 mt-1">Up to <?php echo e($maxSize); ?></p>
                <?php endif; ?>
            </div>
        </label>
        
        <!-- Drag overlay -->
        <div class="drag-overlay absolute inset-0 bg-blue-50 border-2 border-blue-300 rounded-lg hidden items-center justify-center">
            <div class="text-center">
                <svg class="mx-auto h-12 w-12 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                </svg>
                <p class="text-blue-600 font-medium">Drop files here</p>
            </div>
        </div>
    </div>
    
    <!-- File Preview -->
    <div id="<?php echo e($name); ?>-preview" class="mt-4 space-y-2 hidden"></div>
    
    <!-- Upload Progress -->
    <div id="<?php echo e($name); ?>-progress" class="mt-4 hidden">
        <div class="bg-gray-200 rounded-full h-2">
            <div class="bg-blue-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
        </div>
        <p class="text-sm text-gray-600 mt-1">Uploading...</p>
    </div>
</div>

<script>
    // Global file handling functions
    window.fileUploads = window.fileUploads || {};

    function handleDragOver(event) {
        event.preventDefault();
        event.dataTransfer.dropEffect = 'copy';
    }

    function handleDragEnter(event) {
        event.preventDefault();
        const overlay = event.currentTarget.querySelector('.drag-overlay');
        overlay.classList.remove('hidden');
        overlay.classList.add('flex');
    }

    function handleDragLeave(event) {
        event.preventDefault();
        if (!event.currentTarget.contains(event.relatedTarget)) {
            const overlay = event.currentTarget.querySelector('.drag-overlay');
            overlay.classList.add('hidden');
            overlay.classList.remove('flex');
        }
    }

    function handleDrop(event, inputName) {
        event.preventDefault();
        const overlay = event.currentTarget.querySelector('.drag-overlay');
        overlay.classList.add('hidden');
        overlay.classList.remove('flex');
        
        const files = event.dataTransfer.files;
        const input = document.getElementById(inputName);
        
        // Create new FileList
        const dt = new DataTransfer();
        
        // Add existing files
        if (input.files) {
            Array.from(input.files).forEach(file => dt.items.add(file));
        }
        
        // Add new files
        Array.from(files).forEach(file => dt.items.add(file));
        
        input.files = dt.files;
        handleFileSelect(input, inputName);
    }

    function handleFileSelect(input, inputName) {
        const preview = document.getElementById(inputName + '-preview');
        preview.innerHTML = '';
        
        if (input.files && input.files.length > 0) {
            preview.classList.remove('hidden');
            
            Array.from(input.files).forEach((file, index) => {
                const div = document.createElement('div');
                div.className = 'flex items-center space-x-3 p-3 bg-gray-50 rounded-lg border';
                
                // File icon based on type
                let iconClass = 'text-gray-500';
                let iconPath = 'M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z';
                
                if (file.type.startsWith('image/')) {
                    iconClass = 'text-green-500';
                    iconPath = 'M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z';
                } else if (file.type === 'application/pdf') {
                    iconClass = 'text-red-500';
                } else if (file.type.includes('word') || file.type.includes('document')) {
                    iconClass = 'text-blue-500';
                }
                
                div.innerHTML = `
                    <svg class="w-8 h-8 ${iconClass}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="${iconPath}" />
                    </svg>
                    <div class="flex-1 min-w-0">
                        <p class="text-sm font-medium text-gray-900 truncate">${file.name}</p>
                        <p class="text-xs text-gray-500">${formatFileSize(file.size)}</p>
                    </div>
                    <button type="button" onclick="removeFile('${inputName}', ${index})" class="text-red-600 hover:text-red-800 p-1 rounded">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                `;
                
                // Add image preview for image files
                if (file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const img = document.createElement('img');
                        img.src = e.target.result;
                        img.className = 'w-12 h-12 object-cover rounded border ml-2';
                        div.insertBefore(img, div.lastElementChild);
                    };
                    reader.readAsDataURL(file);
                }
                
                preview.appendChild(div);
            });
        } else {
            preview.classList.add('hidden');
        }
    }

    function removeFile(inputName, index) {
        const input = document.getElementById(inputName);
        const dt = new DataTransfer();
        
        Array.from(input.files).forEach((file, i) => {
            if (i !== index) dt.items.add(file);
        });
        
        input.files = dt.files;
        handleFileSelect(input, inputName);
    }

    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Simulate upload progress (for demo purposes)
    function simulateUploadProgress(inputName) {
        const progressContainer = document.getElementById(inputName + '-progress');
        const progressBar = progressContainer.querySelector('.bg-blue-600');
        
        progressContainer.classList.remove('hidden');
        
        let progress = 0;
        const interval = setInterval(() => {
            progress += Math.random() * 15;
            if (progress >= 100) {
                progress = 100;
                clearInterval(interval);
                setTimeout(() => {
                    progressContainer.classList.add('hidden');
                }, 1000);
            }
            progressBar.style.width = progress + '%';
        }, 200);
    }
</script>

<style>
    .file-upload-zone .border-dashed:hover {
        border-color: #9CA3AF;
        background-color: #F9FAFB;
    }
    
    .drag-overlay {
        background-color: rgba(59, 130, 246, 0.05);
        border-color: #3B82F6;
        border-style: dashed;
    }
</style>
<?php /**PATH C:\xampp\htdocs\LARAVEL_PROJECTS\lara_unilink\resources\views/components/file-upload-zone.blade.php ENDPATH**/ ?>