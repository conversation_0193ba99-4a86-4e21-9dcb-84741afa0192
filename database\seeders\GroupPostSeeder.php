<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Group;
use App\Models\Post;
use App\Models\Organization;

class GroupPostSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        // Create a test user
        $user = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => '<PERSON>',
                'password' => bcrypt('password'),
                'email_verified_at' => now(),
            ]
        );

        // Create an organization
        $organization = Organization::firstOrCreate(
            ['slug' => 'mindanao-job-hiring'],
            [
                'name' => 'MINDANAO JOB HIRING 2023 - 2024',
                'description' => 'Job opportunities in Mindanao region',
                'status' => 'active',
                'created_by' => $user->id,
            ]
        );

        // Create a group
        $group = Group::firstOrCreate(
            ['slug' => 'mindanao-job-hiring-group'],
            [
                'name' => 'MINDANAO JOB HIRING 2023 - 2024',
                'description' => 'Connect with job seekers and employers in Mindanao',
                'visibility' => 'public',
                'post_approval' => 'none',
                'allow_file_sharing' => true,
                'max_file_size_mb' => 10,
                'status' => 'active',
                'created_by' => $user->id,
                'organization_id' => $organization->id,
            ]
        );

        // Add user as group admin
        if (!$group->hasActiveMember($user)) {
            $group->members()->attach($user->id, [
                'role' => 'admin',
                'status' => 'active',
                'joined_at' => now()
            ]);
        }

        // Create some test posts in the group
        $posts = [
            [
                'title' => 'Software Developer Position Available',
                'content' => 'We are looking for experienced software developers to join our team in Davao City. Requirements: 3+ years experience in PHP/Laravel, strong problem-solving skills.',
                'type' => 'general'
            ],
            [
                'title' => 'Networking Event This Weekend',
                'content' => 'Join us for a networking event this Saturday at SM Lanang Premier. Great opportunity to meet potential employers and fellow job seekers!',
                'type' => 'event'
            ],
            [
                'title' => 'Resume Writing Tips',
                'content' => 'Here are some essential tips for writing an effective resume:\n\n1. Keep it concise and relevant\n2. Use action verbs\n3. Quantify your achievements\n4. Tailor it to each job application',
                'type' => 'general'
            ]
        ];

        foreach ($posts as $postData) {
            Post::firstOrCreate(
                [
                    'title' => $postData['title'],
                    'group_id' => $group->id,
                    'user_id' => $user->id
                ],
                [
                    'content' => $postData['content'],
                    'type' => $postData['type'],
                    'status' => 'published',
                    'approval_status' => 'approved',
                    'published_at' => now()->subHours(rand(1, 24)),
                ]
            );
        }

        $this->command->info('Group posts seeded successfully!');
        $this->command->info("Visit: http://localhost:8000/groups/{$group->slug}");
        $this->command->info("Login with: <EMAIL> / password");
    }
}
