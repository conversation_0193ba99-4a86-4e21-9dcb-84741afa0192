<?php

namespace App\Listeners;

use App\Events\PostReactionAdded;
use App\Notifications\PostReacted;


class SendPostReactionNotification
{
    private static $processedEvents = [];

    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(PostReactionAdded $event): void
    {
        \Log::info('SendPostReactionNotification::handle called', [
            'user_id' => $event->user->id,
            'post_id' => $event->post->id,
            'reaction_id' => $event->reaction->id
        ]);

        // Create a unique key for this event to prevent duplicate processing
        $eventKey = "post_reaction_{$event->user->id}_{$event->post->id}_{$event->reaction->id}";

        // Check if this event has already been processed
        if (in_array($eventKey, self::$processedEvents)) {
            \Log::info('Skipping duplicate event', ['event_key' => $eventKey]);
            return; // Skip duplicate processing
        }

        // Mark this event as processed
        self::$processedEvents[] = $eventKey;
        \Log::info('Processing event', ['event_key' => $eventKey, 'processed_count' => count(self::$processedEvents)]);

        // Clean up old processed events (keep only last 100)
        if (count(self::$processedEvents) > 100) {
            self::$processedEvents = array_slice(self::$processedEvents, -100);
        }

        // Don't notify if user reacted to their own post
        if ($event->user->id === $event->post->user_id) {
            return;
        }

        // Check if the post owner wants to receive this type of notification
        if (!$event->post->user->wantsNotification('post_reactions')) {
            return;
        }

        // Send notification to the post owner
        $event->post->user->notify(new PostReacted($event->user, $event->post, $event->reaction));
    }
}
