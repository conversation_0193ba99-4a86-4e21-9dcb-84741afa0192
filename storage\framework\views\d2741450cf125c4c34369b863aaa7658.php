<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['post']));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['post']), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars, $__key, $__value); ?>

<div class="post-card bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden"
     data-type="<?php echo e($post->type); ?>"
     data-organization="<?php echo e($post->organization_id ?? ''); ?>"
     data-has-images="<?php echo e($post->images && count($post->images) > 0 ? 'true' : 'false'); ?>"
     data-is-pinned="<?php echo e($post->is_pinned ? 'true' : 'false'); ?>"
     data-post-id="<?php echo e($post->id); ?>">
    <!-- Post Header -->
    <div class="p-4 border-b border-gray-200">
        <div class="flex items-center space-x-3">
            <?php if($post->group): ?>
                <!-- Group Post Header (Facebook Style) -->
                <div class="flex items-center space-x-3">
                    <!-- Group Avatar -->
                    <a href="<?php echo e(route('groups.show', $post->group)); ?>">
                        <img class="h-12 w-12 rounded-full hover:opacity-80 transition-opacity"
                             src="<?php echo e($post->group->logo ? \Illuminate\Support\Facades\Storage::disk('public')->url($post->group->logo) : 'https://ui-avatars.com/api/?name=' . urlencode($post->group->name) . '&color=3B82F6&background=DBEAFE'); ?>"
                             alt="<?php echo e($post->group->name); ?>">
                    </a>

                    <!-- User Avatar (smaller, positioned to overlap slightly) -->
                    <div class="relative -ml-2">
                        <a href="<?php echo e(route('profile.user', $post->user)); ?>">
                            <img class="h-8 w-8 rounded-full border-2 border-white hover:opacity-80 transition-opacity"
                                 src="<?php echo e($post->user->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url($post->user->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($post->user->name) . '&color=7BC74D&background=EEEEEE'); ?>"
                                 alt="<?php echo e($post->user->name); ?>">
                        </a>
                    </div>
                </div>

                <div class="flex-1">
                    <div class="flex items-center space-x-2">
                        <a href="<?php echo e(route('groups.show', $post->group)); ?>" class="text-lg font-semibold text-gray-900 hover:text-custom-green">
                            <?php echo e($post->group->name); ?>

                        </a>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                            <?php if($post->type === 'event'): ?> bg-blue-100 text-blue-800
                            <?php elseif($post->type === 'announcement'): ?> bg-yellow-100 text-yellow-800
                            <?php elseif($post->type === 'financial_report'): ?> bg-green-100 text-green-800
                            <?php else: ?> bg-gray-100 text-gray-800
                            <?php endif; ?>">
                            <?php echo e(ucfirst(str_replace('_', ' ', $post->type))); ?>

                        </span>
                    </div>
                    <div class="flex items-center space-x-1 text-sm text-gray-500">
                        <a href="<?php echo e(route('profile.user', $post->user)); ?>" class="hover:text-custom-green font-medium">
                            <?php echo e($post->user->name); ?>

                        </a>
                        <span>•</span>
                        <span><?php echo e($post->published_at->diffForHumans()); ?></span>
                        <?php if($post->group->visibility === 'private'): ?>
                            <span>•</span>
                            <svg class="w-3 h-3 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd" />
                            </svg>
                        <?php endif; ?>
                    </div>
                </div>
            <?php elseif($post->organization): ?>
                <!-- Organization Post Header -->
                <a href="<?php echo e(route('organizations.show', $post->organization)); ?>">
                    <img class="h-12 w-12 rounded-full hover:opacity-80 transition-opacity"
                         src="<?php echo e($post->organization->logo ? \Illuminate\Support\Facades\Storage::disk('public')->url($post->organization->logo) : 'https://ui-avatars.com/api/?name=' . urlencode($post->organization->name) . '&color=3B82F6&background=DBEAFE'); ?>"
                         alt="<?php echo e($post->organization->name); ?>">
                </a>
                <div class="flex-1">
                    <div class="flex items-center space-x-2">
                        <a href="<?php echo e(route('organizations.show', $post->organization)); ?>" class="text-lg font-semibold text-gray-900 hover:text-custom-green">
                            <?php echo e($post->organization->name); ?>

                        </a>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                            <?php if($post->type === 'event'): ?> bg-blue-100 text-blue-800
                            <?php elseif($post->type === 'announcement'): ?> bg-yellow-100 text-yellow-800
                            <?php elseif($post->type === 'financial_report'): ?> bg-green-100 text-green-800
                            <?php else: ?> bg-gray-100 text-gray-800
                            <?php endif; ?>">
                            <?php echo e(ucfirst(str_replace('_', ' ', $post->type))); ?>

                        </span>
                    </div>
                    <p class="text-sm text-gray-500">
                        by <?php if (isset($component)) { $__componentOriginal77394d5a46b4cdda4ddd01273a4c0a44 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal77394d5a46b4cdda4ddd01273a4c0a44 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.user-link','data' => ['user' => $post->user,'size' => 'xs','showAvatar' => false,'class' => 'inline text-sm text-gray-500 hover:text-custom-green']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('user-link'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['user' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($post->user),'size' => 'xs','show-avatar' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(false),'class' => 'inline text-sm text-gray-500 hover:text-custom-green']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal77394d5a46b4cdda4ddd01273a4c0a44)): ?>
<?php $attributes = $__attributesOriginal77394d5a46b4cdda4ddd01273a4c0a44; ?>
<?php unset($__attributesOriginal77394d5a46b4cdda4ddd01273a4c0a44); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal77394d5a46b4cdda4ddd01273a4c0a44)): ?>
<?php $component = $__componentOriginal77394d5a46b4cdda4ddd01273a4c0a44; ?>
<?php unset($__componentOriginal77394d5a46b4cdda4ddd01273a4c0a44); ?>
<?php endif; ?> •
                        <?php echo e($post->published_at->diffForHumans()); ?>

                    </p>
                </div>
            <?php else: ?>
                <!-- Regular User Post Header -->
                <a href="<?php echo e(route('profile.user', $post->user)); ?>">
                    <img class="h-12 w-12 rounded-full hover:opacity-80 transition-opacity"
                         src="<?php echo e($post->user->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url($post->user->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($post->user->name) . '&color=7BC74D&background=EEEEEE'); ?>"
                         alt="<?php echo e($post->user->name); ?>">
                </a>
                <div class="flex-1">
                    <div class="flex items-center space-x-2">
                        <a href="<?php echo e(route('profile.user', $post->user)); ?>" class="text-lg font-semibold text-gray-900 hover:text-custom-green">
                            <?php echo e($post->user->name); ?>

                        </a>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                            <?php if($post->type === 'event'): ?> bg-blue-100 text-blue-800
                            <?php elseif($post->type === 'announcement'): ?> bg-yellow-100 text-yellow-800
                            <?php elseif($post->type === 'financial_report'): ?> bg-green-100 text-green-800
                            <?php else: ?> bg-gray-100 text-gray-800
                            <?php endif; ?>">
                            <?php echo e(ucfirst(str_replace('_', ' ', $post->type))); ?>

                        </span>
                    </div>
                    <p class="text-sm text-gray-500">
                        <?php echo e($post->published_at->diffForHumans()); ?>

                    </p>
                </div>
            <?php endif; ?>
            
            <!-- Post Actions Dropdown -->
            <?php if(auth()->check() && (auth()->id() === $post->user_id || auth()->user()->isAdmin())): ?>
                <div class="relative" x-data="{ open: false }">
                    <button @click="open = !open" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"/>
                        </svg>
                    </button>
                    <div x-show="open" @click.away="open = false" x-transition class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-10 border">
                        <a href="<?php echo e(route('posts.edit', $post)); ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Edit</a>
                        <form action="<?php echo e(route('posts.destroy', $post)); ?>" method="POST" class="block">
                            <?php echo csrf_field(); ?>
                            <?php echo method_field('DELETE'); ?>
                            <button type="submit" onclick="return confirm('Are you sure you want to delete this post?')" class="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100">Delete</button>
                        </form>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Post Content -->
    <div class="p-4">
        <h4 class="text-xl font-semibold text-gray-900 mb-2"><?php echo e($post->title); ?></h4>
        <div class="text-gray-700 mb-4">
            <?php if(strlen($post->content) > 300): ?>
                <div x-data="{ expanded: false }">
                    <div x-show="!expanded">
                        <?php echo e(Str::limit($post->content, 300)); ?>

                        <button @click="expanded = true" class="text-blue-600 hover:text-blue-800 font-medium">Read more</button>
                    </div>
                    <div x-show="expanded">
                        <?php echo nl2br(e($post->content)); ?>

                        <button @click="expanded = false" class="text-blue-600 hover:text-blue-800 font-medium">Show less</button>
                    </div>
                </div>
            <?php else: ?>
                <?php echo nl2br(e($post->content)); ?>

            <?php endif; ?>
        </div>

        <!-- Images -->
        <?php if($post->images && count($post->images) > 0): ?>
            <div class="mb-4">
                <?php if(count($post->images) === 1): ?>
                    <img src="<?php echo e(\Illuminate\Support\Facades\Storage::disk('public')->url($post->images[0])); ?>"
                         alt="Post image"
                         class="w-full max-h-96 object-cover rounded-lg cursor-pointer hover:opacity-95 transition-opacity"
                         onclick="openImageModal(<?php echo e(json_encode($post->images)); ?>, 0)">
                <?php else: ?>
                    <div class="grid grid-cols-2 gap-2">
                        <?php $__currentLoopData = $post->images; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php if($index < 4): ?>
                                <div class="relative">
                                    <img src="<?php echo e(\Illuminate\Support\Facades\Storage::disk('public')->url($image)); ?>"
                                         alt="Post image"
                                         class="w-full h-48 object-cover rounded-lg cursor-pointer hover:opacity-95 transition-opacity"
                                         onclick="openImageModal(<?php echo e(json_encode($post->images)); ?>, <?php echo e($index); ?>)">
                                    <?php if($index === 3 && count($post->images) > 4): ?>
                                        <div class="absolute inset-0 bg-black bg-opacity-50 rounded-lg flex items-center justify-center cursor-pointer"
                                             onclick="openImageModal(<?php echo e(json_encode($post->images)); ?>, <?php echo e($index); ?>)">
                                            <span class="text-white text-lg font-semibold">+<?php echo e(count($post->images) - 4); ?></span>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                <?php endif; ?>
            </div>
        <?php endif; ?>

        <!-- Facebook Embed -->
        <?php if($post->facebook_embed_url): ?>
            <div class="mb-4">
                <iframe src="<?php echo e($post->facebook_embed_url); ?>" width="100%" height="315" style="border:none;overflow:hidden" scrolling="no" frameborder="0" allowfullscreen="true"></iframe>
            </div>
        <?php endif; ?>

        <!-- File Attachments -->
        <?php if($post->fileAttachments && $post->fileAttachments->count() > 0): ?>
            <div class="mb-4">
                <h5 class="text-sm font-medium text-gray-900 mb-2">Attachments</h5>
                <div class="space-y-2">
                    <?php $__currentLoopData = $post->fileAttachments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $attachment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                            <div class="flex-shrink-0">
                                <i class="<?php echo e(app('App\Services\FileUploadService')->getFileTypeIcon($attachment->file_type)); ?> text-gray-500"></i>
                            </div>
                            <div class="flex-1 min-w-0">
                                <p class="text-sm font-medium text-gray-900 truncate"><?php echo e($attachment->original_filename); ?></p>
                                <p class="text-xs text-gray-500"><?php echo e($attachment->formatted_size); ?></p>
                            </div>
                            <div class="flex-shrink-0">
                                <a href="<?php echo e($attachment->url); ?>" target="_blank"
                                   class="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 transition-colors">
                                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                    </svg>
                                    Download
                                </a>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        <?php endif; ?>

        <!-- Event/Special Content -->
        <?php if($post->type === 'event'): ?>
            <div class="bg-blue-50 rounded-lg p-3 mb-4">
                <div class="text-sm text-blue-800">
                    <div class="flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a1 1 0 011-1h6a1 1 0 011 1v4h3a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9a2 2 0 012-2h3z" />
                        </svg>
                        Event Details
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <!-- Reaction Summary Bar -->
    <?php
        $totalReactions = $post->reactions()->count();
        $totalComments = $post->comments->count();
        $totalShares = $post->shares->count();

        // Get top 3 reaction types for display
        $topReactions = $post->reactions()
            ->select('type', \DB::raw('count(*) as count'))
            ->groupBy('type')
            ->orderBy('count', 'desc')
            ->limit(3)
            ->get();
    ?>

    <div id="summary-bar-<?php echo e($post->id); ?>" class="px-4 py-3 border-t border-gray-200" style="display: <?php echo e(($totalReactions > 0 || $totalComments > 0 || $totalShares > 0) ? 'block' : 'none'); ?>">
        <div class="flex items-center justify-between text-sm text-gray-600">
            <!-- Left: Reaction emojis and count -->
            <div class="flex items-center space-x-2">
                <div id="reaction-summary-<?php echo e($post->id); ?>" class="flex items-center space-x-1" style="display: <?php echo e($totalReactions > 0 ? 'flex' : 'none'); ?>">
                    <!-- Show top reaction emojis -->
                    <div id="reaction-emojis-<?php echo e($post->id); ?>" class="flex -space-x-1">
                        <?php $__currentLoopData = $topReactions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $reaction): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php
                                $reactionDetails = \App\Models\Reaction::getReactionDetails($reaction->type);
                            ?>
                            <?php if($reactionDetails): ?>
                                <div class="w-5 h-5 rounded-full bg-white border border-gray-200 flex items-center justify-center">
                                    <img src="<?php echo e($reactionDetails['emoji']); ?>" alt="<?php echo e($reactionDetails['label']); ?>" class="w-4 h-4">
                                </div>
                            <?php endif; ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                    <span id="reaction-count-<?php echo e($post->id); ?>" class="font-medium"><?php echo e($totalReactions); ?></span>
                </div>
            </div>

            <!-- Right: Comments and shares -->
            <div class="flex items-center space-x-4">
                <span id="comment-summary-<?php echo e($post->id); ?>" class="hover:underline cursor-pointer" onclick="openCommentModal(<?php echo e($post->id); ?>)" style="display: <?php echo e($totalComments > 0 ? 'inline' : 'none'); ?>">
                    <?php echo e($totalComments); ?> comment<?php echo e($totalComments !== 1 ? 's' : ''); ?>

                </span>
                <span id="share-summary-<?php echo e($post->id); ?>" style="display: <?php echo e($totalShares > 0 ? 'inline' : 'none'); ?>">
                    <?php echo e($totalShares); ?> share<?php echo e($totalShares !== 1 ? 's' : ''); ?>

                </span>
            </div>
        </div>
    </div>

    <!-- Post Actions -->
    <div class="px-4 py-3 bg-gray-50 border-t border-gray-200">
        <div class="flex items-center justify-between">
            <div class="flex items-center justify-around w-full">
                <!-- Facebook-style Reactions -->
                <?php if (isset($component)) { $__componentOriginala21421101ecd1a67ffa8905ddbe200b3 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginala21421101ecd1a67ffa8905ddbe200b3 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.facebook-reactions','data' => ['target' => $post,'targetType' => 'post']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('facebook-reactions'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['target' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($post),'target-type' => 'post']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginala21421101ecd1a67ffa8905ddbe200b3)): ?>
<?php $attributes = $__attributesOriginala21421101ecd1a67ffa8905ddbe200b3; ?>
<?php unset($__attributesOriginala21421101ecd1a67ffa8905ddbe200b3); ?>
<?php endif; ?>
<?php if (isset($__componentOriginala21421101ecd1a67ffa8905ddbe200b3)): ?>
<?php $component = $__componentOriginala21421101ecd1a67ffa8905ddbe200b3; ?>
<?php unset($__componentOriginala21421101ecd1a67ffa8905ddbe200b3); ?>
<?php endif; ?>

                <button onclick="openCommentModal(<?php echo e($post->id); ?>)" class="flex items-center space-x-2 text-gray-500 hover:text-blue-600 transition-colors py-2 px-4 rounded-lg hover:bg-gray-100">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                    </svg>
                    <span class="text-sm font-medium">Comment</span>
                </button>

                <button onclick="openShareModal(<?php echo e($post->id); ?>)" class="flex items-center space-x-2 text-gray-500 hover:text-blue-600 transition-colors py-2 px-4 rounded-lg hover:bg-gray-100">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                    </svg>
                    <span class="text-sm font-medium">Share</span>
                </button>
            </div>

            <?php if($post->type === 'event'): ?>
                <a href="<?php echo e(route('posts.show', $post)); ?>" class="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 ml-4">
                    View Details
                </a>
            <?php endif; ?>
        </div>
    </div>

    <!-- Inline Comments Section (hidden by default) -->
    <div id="comments-section-<?php echo e($post->id); ?>" class="hidden border-t border-gray-200">
        <?php if (isset($component)) { $__componentOriginal66018e910bc6d817aae1b7bce4b9c31a = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal66018e910bc6d817aae1b7bce4b9c31a = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.comment-section','data' => ['post' => $post,'showInline' => true]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('comment-section'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['post' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($post),'showInline' => true]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal66018e910bc6d817aae1b7bce4b9c31a)): ?>
<?php $attributes = $__attributesOriginal66018e910bc6d817aae1b7bce4b9c31a; ?>
<?php unset($__attributesOriginal66018e910bc6d817aae1b7bce4b9c31a); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal66018e910bc6d817aae1b7bce4b9c31a)): ?>
<?php $component = $__componentOriginal66018e910bc6d817aae1b7bce4b9c31a; ?>
<?php unset($__componentOriginal66018e910bc6d817aae1b7bce4b9c31a); ?>
<?php endif; ?>
    </div>

    <!-- Comment Modal -->
    <?php if (isset($component)) { $__componentOriginal8f09e7eba7cea483428a19f1e505735b = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal8f09e7eba7cea483428a19f1e505735b = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.comment-modal','data' => ['post' => $post]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('comment-modal'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['post' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($post)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal8f09e7eba7cea483428a19f1e505735b)): ?>
<?php $attributes = $__attributesOriginal8f09e7eba7cea483428a19f1e505735b; ?>
<?php unset($__attributesOriginal8f09e7eba7cea483428a19f1e505735b); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal8f09e7eba7cea483428a19f1e505735b)): ?>
<?php $component = $__componentOriginal8f09e7eba7cea483428a19f1e505735b; ?>
<?php unset($__componentOriginal8f09e7eba7cea483428a19f1e505735b); ?>
<?php endif; ?>

    <!-- Share Modal -->
    <?php if (isset($component)) { $__componentOriginal0dc3624784f5ac950932d2feff9a6435 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal0dc3624784f5ac950932d2feff9a6435 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.share-modal','data' => ['post' => $post]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('share-modal'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['post' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($post)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal0dc3624784f5ac950932d2feff9a6435)): ?>
<?php $attributes = $__attributesOriginal0dc3624784f5ac950932d2feff9a6435; ?>
<?php unset($__attributesOriginal0dc3624784f5ac950932d2feff9a6435); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal0dc3624784f5ac950932d2feff9a6435)): ?>
<?php $component = $__componentOriginal0dc3624784f5ac950932d2feff9a6435; ?>
<?php unset($__componentOriginal0dc3624784f5ac950932d2feff9a6435); ?>
<?php endif; ?>
</div>

<script>
// toggleLike function is now handled by comment-modal.js for consistency

// Toggle comments section
function toggleComments(postId) {
    const commentsSection = document.getElementById(`comments-section-${postId}`);
    if (commentsSection.classList.contains('hidden')) {
        commentsSection.classList.remove('hidden');
    } else {
        commentsSection.classList.add('hidden');
    }
}

// Share Modal Functions
function openShareModal(postId) {
    document.getElementById(`shareModal-${postId}`).classList.remove('hidden');
}

function closeShareModal(postId) {
    document.getElementById(`shareModal-${postId}`).classList.add('hidden');
}

// Social Media Sharing
function shareToFacebook(postId) {
    const url = encodeURIComponent(window.location.origin + `/posts/${postId}`);
    window.open(`https://www.facebook.com/sharer/sharer.php?u=${url}`, '_blank', 'width=600,height=400');
    recordExternalShare(postId, 'facebook');
}

function shareToTwitter(postId) {
    const url = encodeURIComponent(window.location.origin + `/posts/${postId}`);
    const text = encodeURIComponent('Check out this post!');
    window.open(`https://twitter.com/intent/tweet?url=${url}&text=${text}`, '_blank', 'width=600,height=400');
    recordExternalShare(postId, 'twitter');
}

function shareToLinkedIn(postId) {
    const url = encodeURIComponent(window.location.origin + `/posts/${postId}`);
    window.open(`https://www.linkedin.com/sharing/share-offsite/?url=${url}`, '_blank', 'width=600,height=400');
    recordExternalShare(postId, 'linkedin');
}

function copyPostLink(postId) {
    const url = window.location.origin + `/posts/${postId}`;
    navigator.clipboard.writeText(url).then(() => {
        alert('Link copied to clipboard!');
        recordExternalShare(postId, 'copy_link');
    });
}

// Record external share
async function recordExternalShare(postId, platform) {
    try {
        const response = await fetch(`/posts/${postId}/share/external`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({ platform: platform })
        });

        const data = await response.json();

        if (data.success && window.postSummaryUpdater) {
            // Trigger real-time summary update
            window.postSummaryUpdater.onShareChange(postId);
        }
    } catch (error) {
        console.error('Error recording share:', error);
    }
}
</script>
<?php /**PATH C:\xampp\htdocs\LARAVEL_PROJECTS\lara_unilink\resources\views/components/post-card.blade.php ENDPATH**/ ?>