<?php

use App\Models\User;
use App\Models\Post;
use App\Models\Group;
use App\Models\Organization;
use App\Models\Attachment;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;

test('normal post can have file attachments', function () {
    Storage::fake('public');
    
    $user = User::factory()->create();
    
    // Create a fake file
    $file = UploadedFile::fake()->create('test-document.pdf', 100, 'application/pdf');
    
    $response = $this->actingAs($user)->post('/posts', [
        'title' => 'Test Post with Attachment',
        'content' => 'This post has a file attachment',
        'type' => 'general',
        'status' => 'published',
        'attachments' => [$file]
    ]);
    
    $response->assertRedirect();
    
    // Check that the post was created
    $this->assertDatabaseHas('posts', [
        'title' => 'Test Post with Attachment',
        'user_id' => $user->id,
        'group_id' => null,
        'organization_id' => null
    ]);
    
    // Check that the attachment was created
    $post = Post::where('title', 'Test Post with Attachment')->first();
    $this->assertNotNull($post);
    
    $this->assertDatabaseHas('attachments', [
        'post_id' => $post->id,
        'original_filename' => 'test-document.pdf',
        'file_type' => 'pdf'
    ]);
    
    // Check that the file was stored
    $attachment = Attachment::where('post_id', $post->id)->first();
    Storage::disk('public')->assertExists($attachment->file_path);
});

test('organization post can have file attachments', function () {
    Storage::fake('public');
    
    $user = User::factory()->create();
    $organization = Organization::factory()->create();
    
    // Add user to organization
    $organization->members()->attach($user->id, [
        'role' => 'officer',
        'status' => 'active',
        'joined_at' => now()
    ]);
    
    // Create a fake file
    $file = UploadedFile::fake()->create('org-document.docx', 200, 'application/vnd.openxmlformats-officedocument.wordprocessingml.document');
    
    $response = $this->actingAs($user)->post('/posts', [
        'title' => 'Organization Post with Attachment',
        'content' => 'This organization post has a file attachment',
        'type' => 'announcement',
        'status' => 'published',
        'organization_id' => $organization->id,
        'attachments' => [$file]
    ]);
    
    $response->assertRedirect();
    
    // Check that the post was created
    $this->assertDatabaseHas('posts', [
        'title' => 'Organization Post with Attachment',
        'user_id' => $user->id,
        'organization_id' => $organization->id,
        'group_id' => null
    ]);
    
    // Check that the attachment was created
    $post = Post::where('title', 'Organization Post with Attachment')->first();
    $this->assertNotNull($post);
    
    $this->assertDatabaseHas('attachments', [
        'post_id' => $post->id,
        'original_filename' => 'org-document.docx',
        'file_type' => 'docx'
    ]);
    
    // Check that the file was stored in the organizations directory
    $attachment = Attachment::where('post_id', $post->id)->first();
    expect($attachment->file_path)->toContain('organizations/attachments');
    Storage::disk('public')->assertExists($attachment->file_path);
});

test('normal post respects file size limits', function () {
    Storage::fake('public');
    
    $user = User::factory()->create();
    
    // Create a file that's too large (over 10MB)
    $file = UploadedFile::fake()->create('large-file.pdf', 11 * 1024, 'application/pdf'); // 11MB
    
    $response = $this->actingAs($user)->post('/posts', [
        'title' => 'Test Post with Large File',
        'content' => 'This post has a large file attachment',
        'type' => 'general',
        'status' => 'published',
        'attachments' => [$file]
    ]);
    
    $response->assertRedirect();
    
    // Check that the post was created
    $post = Post::where('title', 'Test Post with Large File')->first();
    $this->assertNotNull($post);
    
    // Check that no attachment was created due to size limit
    $this->assertDatabaseMissing('attachments', [
        'post_id' => $post->id
    ]);
});

test('normal post respects allowed file types', function () {
    Storage::fake('public');
    
    $user = User::factory()->create();
    
    // Create a file with disallowed extension
    $file = UploadedFile::fake()->create('script.exe', 100, 'application/x-msdownload');
    
    $response = $this->actingAs($user)->post('/posts', [
        'title' => 'Test Post with Disallowed File',
        'content' => 'This post has a disallowed file type',
        'type' => 'general',
        'status' => 'published',
        'attachments' => [$file]
    ]);
    
    $response->assertRedirect();
    
    // Check that the post was created
    $post = Post::where('title', 'Test Post with Disallowed File')->first();
    $this->assertNotNull($post);
    
    // Check that no attachment was created due to file type restriction
    $this->assertDatabaseMissing('attachments', [
        'post_id' => $post->id
    ]);
});

test('normal post can have multiple attachments', function () {
    Storage::fake('public');
    
    $user = User::factory()->create();
    
    // Create multiple files
    $file1 = UploadedFile::fake()->create('document1.pdf', 100, 'application/pdf');
    $file2 = UploadedFile::fake()->create('image1.jpg', 50, 'image/jpeg');
    $file3 = UploadedFile::fake()->create('archive.zip', 200, 'application/zip');
    
    $response = $this->actingAs($user)->post('/posts', [
        'title' => 'Test Post with Multiple Attachments',
        'content' => 'This post has multiple file attachments',
        'type' => 'general',
        'status' => 'published',
        'attachments' => [$file1, $file2, $file3]
    ]);
    
    $response->assertRedirect();
    
    // Check that the post was created
    $post = Post::where('title', 'Test Post with Multiple Attachments')->first();
    $this->assertNotNull($post);
    
    // Check that all attachments were created
    $this->assertEquals(3, $post->fileAttachments()->count());
    
    // Check specific attachments
    $this->assertDatabaseHas('attachments', [
        'post_id' => $post->id,
        'original_filename' => 'document1.pdf',
        'file_type' => 'pdf'
    ]);
    
    $this->assertDatabaseHas('attachments', [
        'post_id' => $post->id,
        'original_filename' => 'image1.jpg',
        'file_type' => 'jpg'
    ]);
    
    $this->assertDatabaseHas('attachments', [
        'post_id' => $post->id,
        'original_filename' => 'archive.zip',
        'file_type' => 'zip'
    ]);
});

test('group post still works with file attachments when file sharing is enabled', function () {
    Storage::fake('public');
    
    $user = User::factory()->create();
    $group = Group::factory()->create([
        'created_by' => $user->id,
        'allow_file_sharing' => true,
        'max_file_size_mb' => 5,
        'allowed_file_types' => ['pdf', 'jpg', 'png']
    ]);
    
    // Add user as member
    $group->members()->attach($user->id, [
        'role' => 'admin',
        'status' => 'active',
        'joined_at' => now()
    ]);
    
    // Create a file within group limits
    $file = UploadedFile::fake()->create('group-doc.pdf', 100, 'application/pdf');
    
    $response = $this->actingAs($user)->post('/posts', [
        'title' => 'Group Post with Attachment',
        'content' => 'This group post has a file attachment',
        'type' => 'general',
        'status' => 'published',
        'group_id' => $group->id,
        'attachments' => [$file]
    ]);
    
    $response->assertRedirect();
    
    // Check that the post was created
    $post = Post::where('title', 'Group Post with Attachment')->first();
    $this->assertNotNull($post);
    
    // Check that the attachment was created
    $this->assertDatabaseHas('attachments', [
        'post_id' => $post->id,
        'original_filename' => 'group-doc.pdf',
        'file_type' => 'pdf'
    ]);
    
    // Check that the file was stored in the groups directory
    $attachment = Attachment::where('post_id', $post->id)->first();
    expect($attachment->file_path)->toContain('groups/attachments');
    Storage::disk('public')->assertExists($attachment->file_path);
});

test('post creator can add attachments when editing post', function () {
    Storage::fake('public');

    $user = User::factory()->create();
    $post = Post::factory()->create(['user_id' => $user->id]);

    // Create a new file to add
    $file = UploadedFile::fake()->create('new-document.pdf', 100, 'application/pdf');

    $response = $this->actingAs($user)->put("/posts/{$post->id}", [
        'title' => $post->title,
        'content' => $post->content,
        'type' => $post->type,
        'organization_id' => $post->organization_id,
        'status' => 'published',
        'is_pinned' => false,
        'attachments' => [$file]
    ]);

    $response->assertRedirect();

    // Check that the attachment was added
    $this->assertDatabaseHas('attachments', [
        'post_id' => $post->id,
        'original_filename' => 'new-document.pdf',
        'file_type' => 'pdf'
    ]);

    // Check that the file was stored
    $attachment = Attachment::where('post_id', $post->id)->first();
    Storage::disk('public')->assertExists($attachment->file_path);
});

test('post creator can remove attachments when editing post', function () {
    Storage::fake('public');

    $user = User::factory()->create();
    $post = Post::factory()->create(['user_id' => $user->id]);

    // Create an existing attachment
    $file = UploadedFile::fake()->create('existing-document.pdf', 100, 'application/pdf');
    $path = $file->store('posts/attachments', 'public');

    $attachment = $post->fileAttachments()->create([
        'filename' => basename($path),
        'file_path' => $path,
        'original_filename' => 'existing-document.pdf',
        'file_type' => 'pdf',
        'file_size' => $file->getSize(),
        'mime_type' => 'application/pdf',
    ]);

    // Verify file exists
    Storage::disk('public')->assertExists($path);

    $response = $this->actingAs($user)->put("/posts/{$post->id}", [
        'title' => $post->title,
        'content' => $post->content,
        'type' => $post->type,
        'organization_id' => $post->organization_id,
        'status' => 'published',
        'is_pinned' => false,
        'remove_attachments' => [$attachment->id]
    ]);

    $response->assertRedirect();

    // Check that the attachment was removed from database
    $this->assertDatabaseMissing('attachments', [
        'id' => $attachment->id
    ]);

    // Check that the file was deleted from storage
    Storage::disk('public')->assertMissing($path);
});

test('post creator can add and remove attachments simultaneously', function () {
    Storage::fake('public');

    $user = User::factory()->create();
    $post = Post::factory()->create(['user_id' => $user->id]);

    // Create an existing attachment
    $existingFile = UploadedFile::fake()->create('existing-document.pdf', 100, 'application/pdf');
    $existingPath = $existingFile->store('posts/attachments', 'public');

    $existingAttachment = $post->fileAttachments()->create([
        'filename' => basename($existingPath),
        'file_path' => $existingPath,
        'original_filename' => 'existing-document.pdf',
        'file_type' => 'pdf',
        'file_size' => $existingFile->getSize(),
        'mime_type' => 'application/pdf',
    ]);

    // Create a new file to add
    $newFile = UploadedFile::fake()->create('new-document.docx', 200, 'application/vnd.openxmlformats-officedocument.wordprocessingml.document');

    $response = $this->actingAs($user)->put("/posts/{$post->id}", [
        'title' => $post->title,
        'content' => $post->content,
        'type' => $post->type,
        'organization_id' => $post->organization_id,
        'status' => 'published',
        'is_pinned' => false,
        'attachments' => [$newFile],
        'remove_attachments' => [$existingAttachment->id]
    ]);

    $response->assertRedirect();

    // Check that the old attachment was removed
    $this->assertDatabaseMissing('attachments', [
        'id' => $existingAttachment->id
    ]);
    Storage::disk('public')->assertMissing($existingPath);

    // Check that the new attachment was added
    $this->assertDatabaseHas('attachments', [
        'post_id' => $post->id,
        'original_filename' => 'new-document.docx',
        'file_type' => 'docx'
    ]);

    $newAttachment = Attachment::where('post_id', $post->id)->where('file_type', 'docx')->first();
    Storage::disk('public')->assertExists($newAttachment->file_path);
});

test('non-owner cannot edit post attachments', function () {
    Storage::fake('public');

    $owner = User::factory()->create();
    $otherUser = User::factory()->create();
    $post = Post::factory()->create(['user_id' => $owner->id]);

    $file = UploadedFile::fake()->create('unauthorized.pdf', 100, 'application/pdf');

    $response = $this->actingAs($otherUser)->put("/posts/{$post->id}", [
        'title' => $post->title,
        'content' => $post->content,
        'type' => $post->type,
        'organization_id' => $post->organization_id,
        'status' => 'published',
        'is_pinned' => false,
        'attachments' => [$file]
    ]);

    $response->assertStatus(403);

    // Check that no attachment was created
    $this->assertDatabaseMissing('attachments', [
        'post_id' => $post->id,
        'original_filename' => 'unauthorized.pdf'
    ]);
});
