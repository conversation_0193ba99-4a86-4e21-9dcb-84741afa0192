<?php

namespace App\Notifications;

use App\Models\User;
use App\Models\Group;
use App\Models\Post;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\BroadcastMessage;
use Illuminate\Notifications\Notification;

class GroupPostPendingApproval extends Notification implements ShouldQueue
{
    use Queueable;

    public $post;
    public $group;
    public $author;

    /**
     * Create a new notification instance.
     */
    public function __construct(Post $post, Group $group, User $author)
    {
        $this->post = $post;
        $this->group = $group;
        $this->author = $author;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['database', 'broadcast'];
    }

    /**
     * Get the broadcastable representation of the notification.
     */
    public function toBroadcast(object $notifiable): BroadcastMessage
    {
        return new BroadcastMessage([
            'id' => $this->id,
            'type' => 'group_post_pending',
            'user_id' => $this->author->id,
            'user_name' => $this->author->name,
            'user_avatar' => $this->author->getNotificationAvatarUrl(),
            'post_id' => $this->post->id,
            'post_title' => $this->post->title,
            'group_id' => $this->group->id,
            'group_name' => $this->group->name,
            'group_logo' => $this->group->logo ? \Illuminate\Support\Facades\Storage::disk('public')->url($this->group->logo) : null,
            'message' => $this->getMessage(),
            'url' => $this->getApprovalUrl(),
            'created_at' => now()->toISOString(),
        ]);
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'type' => 'group_post_pending',
            'user_id' => $this->author->id,
            'user_name' => $this->author->name,
            'user_avatar' => $this->author->getNotificationAvatarUrl(),
            'post_id' => $this->post->id,
            'post_title' => $this->post->title,
            'group_id' => $this->group->id,
            'group_name' => $this->group->name,
            'group_logo' => $this->group->logo ? \Illuminate\Support\Facades\Storage::disk('public')->url($this->group->logo) : null,
            'message' => $this->getMessage(),
            'url' => $this->getApprovalUrl(),
        ];
    }

    /**
     * Get the notification message
     */
    private function getMessage(): string
    {
        return "{$this->author->name} submitted a post for approval in {$this->group->name}";
    }

    /**
     * Get the approval URL
     */
    private function getApprovalUrl(): string
    {
        return route('groups.pending-posts', $this->group->slug);
    }
}
