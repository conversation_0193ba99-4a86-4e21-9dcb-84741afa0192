<?php

namespace App\Events;

use App\Models\User;
use App\Models\Comment;
use App\Models\Reaction;

use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class CommentReactionAdded
{
    use Dispatchable, SerializesModels;

    public $user;
    public $comment;
    public $reaction;

    /**
     * Create a new event instance.
     */
    public function __construct(User $user, Comment $comment, Reaction $reaction)
    {
        $this->user = $user;
        $this->comment = $comment;
        $this->reaction = $reaction;
    }


}
