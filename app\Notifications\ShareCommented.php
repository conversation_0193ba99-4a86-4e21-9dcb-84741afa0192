<?php

namespace App\Notifications;

use App\Models\User;
use App\Models\Share;
use App\Models\Comment;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\BroadcastMessage;
use Illuminate\Notifications\Notification;

class ShareCommented extends Notification implements ShouldQueue
{
    use Queueable;

    public $user;
    public $share;
    public $comment;

    /**
     * Create a new notification instance.
     */
    public function __construct(User $user, Share $share, Comment $comment)
    {
        $this->user = $user;
        $this->share = $share;
        $this->comment = $comment;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['database', 'broadcast'];
    }

    /**
     * Get the broadcastable representation of the notification.
     */
    public function toBroadcast(object $notifiable): BroadcastMessage
    {
        return new BroadcastMessage([
            'id' => $this->id,
            'type' => 'share_commented',
            'user_id' => $this->user->id,
            'user_name' => $this->user->name,
            'user_avatar' => $this->user->getNotificationAvatarUrl(),
            'share_id' => $this->share->id,
            'comment_id' => $this->comment->id,
            'comment_content' => $this->comment->content,
            'post_id' => $this->share->post->id,
            'post_title' => $this->share->post->title,
            'message' => $this->getMessage(),
            'url' => $this->getShareUrl(),
            'created_at' => now()->toISOString(),
        ]);
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'type' => 'share_commented',
            'user_id' => $this->user->id,
            'user_name' => $this->user->name,
            'user_avatar' => $this->user->getNotificationAvatarUrl(),
            'share_id' => $this->share->id,
            'comment_id' => $this->comment->id,
            'comment_content' => $this->comment->content,
            'post_id' => $this->share->post->id,
            'post_title' => $this->share->post->title,
            'message' => $this->getMessage(),
            'url' => $this->getShareUrl(),
        ];
    }

    /**
     * Get the notification message.
     */
    private function getMessage(): string
    {
        return "{$this->user->name} commented on your shared post";
    }

    /**
     * Get the share URL.
     */
    private function getShareUrl(): string
    {
        // For now, redirect to the original post
        // In the future, you might want to create a dedicated share view
        return route('posts.show', $this->share->post->id);
    }
}
