<x-unilink-layout>
    <div class="max-w-4xl mx-auto">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
            <div class="flex items-center space-x-4">
                <img src="{{ $user->avatar ? asset('storage/' . $user->avatar) : asset('images/default-avatar.png') }}" 
                     alt="{{ $user->name }}" 
                     class="w-16 h-16 rounded-full object-cover">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">{{ $user->name }}'s Following</h1>
                    <p class="text-gray-600">{{ number_format($following->total()) }} following</p>
                </div>
            </div>
            
            <!-- Navigation -->
            <div class="mt-6 flex space-x-4">
                <a href="{{ route('profile.user', $user) }}" 
                   class="text-gray-600 hover:text-blue-600 transition-colors">
                    Profile
                </a>
                <a href="{{ route('users.followers', $user) }}" 
                   class="text-gray-600 hover:text-blue-600 transition-colors">
                    Followers
                </a>
                <span class="text-blue-600 font-medium">Following</span>
            </div>
        </div>

        <!-- Following List -->
        <div class="bg-white rounded-lg shadow-sm">
            @if($following->count() > 0)
                <div class="divide-y divide-gray-200">
                    @foreach($following as $followedUser)
                        <div class="p-6 flex items-center justify-between">
                            <div class="flex items-center space-x-4">
                                <img src="{{ $followedUser->avatar ? asset('storage/' . $followedUser->avatar) : asset('images/default-avatar.png') }}" 
                                     alt="{{ $followedUser->name }}" 
                                     class="w-12 h-12 rounded-full object-cover">
                                <div>
                                    <h3 class="font-medium text-gray-900">
                                        <a href="{{ route('profile.user', $followedUser) }}" 
                                           class="hover:text-blue-600 transition-colors">
                                            {{ $followedUser->name }}
                                        </a>
                                    </h3>
                                    @if($followedUser->bio)
                                        <p class="text-sm text-gray-600 mt-1">{{ Str::limit($followedUser->bio, 100) }}</p>
                                    @endif
                                    <p class="text-xs text-gray-500 mt-1">
                                        {{ $followedUser->followers()->count() }} followers • 
                                        {{ $followedUser->following()->count() }} following
                                    </p>
                                </div>
                            </div>
                            
                            @auth
                                @if(auth()->id() !== $followedUser->id)
                                    <livewire:user-follower :user="$followedUser" :key="'following-' . $followedUser->id" />
                                @endif
                            @endauth
                        </div>
                    @endforeach
                </div>

                <!-- Pagination -->
                <div class="px-6 py-4 border-t border-gray-200">
                    {{ $following->links() }}
                </div>
            @else
                <div class="p-12 text-center">
                    <svg class="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Not following anyone yet</h3>
                    <p class="text-gray-600">{{ $user->name }} isn't following anyone yet.</p>
                </div>
            @endif
        </div>
    </div>
</x-unilink-layout>
