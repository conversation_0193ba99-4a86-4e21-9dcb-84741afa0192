<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class FollowerController extends Controller
{
    /**
     * Follow a user
     */
    public function follow(User $user)
    {
        $currentUser = Auth::user();

        if ($currentUser->id === $user->id) {
            return back()->with('error', 'You cannot follow yourself.');
        }

        if ($currentUser->isFollowingUser($user)) {
            return back()->with('error', 'You are already following this user.');
        }

        $currentUser->followUser($user);

        return back()->with('success', "You are now following {$user->name}!");
    }

    /**
     * Unfollow a user
     */
    public function unfollow(User $user)
    {
        $currentUser = Auth::user();

        if (!$currentUser->isFollowingUser($user)) {
            return back()->with('error', 'You are not following this user.');
        }

        $currentUser->unfollowUser($user);

        return back()->with('success', "You have unfollowed {$user->name}.");
    }

    /**
     * Show followers of a user
     */
    public function followers(User $user)
    {
        $followers = $user->followers()
            ->orderBy('user_followers.created_at', 'desc')
            ->paginate(20);

        return view('users.followers', compact('user', 'followers'));
    }

    /**
     * Show users that a user is following
     */
    public function following(User $user)
    {
        $following = $user->following()
            ->orderBy('user_followers.created_at', 'desc')
            ->paginate(20);

        return view('users.following', compact('user', 'following'));
    }

    /**
     * Toggle follow status via AJAX
     */
    public function toggleFollow(User $user)
    {
        $currentUser = Auth::user();

        if ($currentUser->id === $user->id) {
            return response()->json([
                'success' => false,
                'message' => 'You cannot follow yourself.'
            ], 400);
        }

        $isFollowing = $currentUser->isFollowingUser($user);

        if ($isFollowing) {
            $currentUser->unfollowUser($user);
            $message = "You have unfollowed {$user->name}.";
        } else {
            $currentUser->followUser($user);
            $message = "You are now following {$user->name}!";
        }

        return response()->json([
            'success' => true,
            'is_following' => !$isFollowing,
            'followers_count' => $user->followers()->count(),
            'message' => $message
        ]);
    }
}
