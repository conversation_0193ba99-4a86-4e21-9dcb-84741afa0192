<?php

namespace App\Notifications;

use App\Models\User;
use App\Models\Group;
use App\Models\Post;
use Illuminate\Notifications\Messages\BroadcastMessage;
use Illuminate\Notifications\Notification;

class GroupPostApproved extends Notification
{

    public $post;
    public $group;
    public $approver;

    /**
     * Create a new notification instance.
     */
    public function __construct(Post $post, Group $group, User $approver)
    {
        $this->post = $post;
        $this->group = $group;
        $this->approver = $approver;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        $channels = [];

        // Check if user wants to receive group post approval notifications
        if ($notifiable->wantsNotification('group_post_approvals')) {
            $channels[] = 'database';
            $channels[] = 'broadcast';
        }

        return $channels;
    }

    /**
     * Get the broadcastable representation of the notification.
     */
    public function toBroadcast(object $notifiable): BroadcastMessage
    {
        return new BroadcastMessage([
            'id' => $this->id,
            'type' => 'group_post_approved',
            'user_id' => $this->approver->id,
            'user_name' => $this->approver->name,
            'user_avatar' => $this->approver->getNotificationAvatarUrl(),
            'post_id' => $this->post->id,
            'post_title' => $this->post->title,
            'group_id' => $this->group->id,
            'group_name' => $this->group->name,
            'group_logo' => $this->group->logo ? \Illuminate\Support\Facades\Storage::disk('public')->url($this->group->logo) : null,
            'message' => $this->getMessage(),
            'url' => $this->getPostUrl(),
            'created_at' => now()->toISOString(),
        ]);
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'type' => 'group_post_approved',
            'user_id' => $this->approver->id,
            'user_name' => $this->approver->name,
            'user_avatar' => $this->approver->getNotificationAvatarUrl(),
            'post_id' => $this->post->id,
            'post_title' => $this->post->title,
            'group_id' => $this->group->id,
            'group_name' => $this->group->name,
            'group_logo' => $this->group->logo ? \Illuminate\Support\Facades\Storage::disk('public')->url($this->group->logo) : null,
            'message' => $this->getMessage(),
            'url' => $this->getPostUrl(),
        ];
    }

    /**
     * Get the notification message
     */
    private function getMessage(): string
    {
        return "Your post \"{$this->post->title}\" was approved in {$this->group->name}";
    }

    /**
     * Get the post URL
     */
    private function getPostUrl(): string
    {
        return route('groups.show', $this->group->slug) . '#post-' . $this->post->id;
    }
}
