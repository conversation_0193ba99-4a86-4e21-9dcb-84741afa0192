<?php

namespace App\Notifications;

use App\Models\User;
use App\Models\Post;
use App\Models\Group;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\BroadcastMessage;
use Illuminate\Notifications\Notification;

class GroupPostCreated extends Notification implements ShouldQueue
{
    use Queueable;

    public $group;
    public $post;
    public $author;

    /**
     * Create a new notification instance.
     */
    public function __construct(Group $group, Post $post, User $author)
    {
        $this->group = $group;
        $this->post = $post;
        $this->author = $author;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['database', 'broadcast'];
    }

    /**
     * Get the broadcastable representation of the notification.
     */
    public function toBroadcast(object $notifiable): BroadcastMessage
    {
        return new BroadcastMessage([
            'id' => $this->id,
            'type' => 'group_post_created',
            'group_id' => $this->group->id,
            'group_name' => $this->group->name,
            'group_logo' => $this->group->logo ? \Illuminate\Support\Facades\Storage::disk('public')->url($this->group->logo) : null,
            'author_id' => $this->author->id,
            'author_name' => $this->author->name,
            'post_id' => $this->post->id,
            'post_title' => $this->post->title,
            'post_content' => $this->getPostPreview(),
            'message' => $this->getMessage(),
            'url' => $this->getPostUrl(),
            'created_at' => now()->toISOString(),
        ]);
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'type' => 'group_post_created',
            'group_id' => $this->group->id,
            'group_name' => $this->group->name,
            'group_logo' => $this->group->logo ? \Illuminate\Support\Facades\Storage::disk('public')->url($this->group->logo) : null,
            'author_id' => $this->author->id,
            'author_name' => $this->author->name,
            'post_id' => $this->post->id,
            'post_title' => $this->post->title,
            'post_content' => $this->getPostPreview(),
            'message' => $this->getMessage(),
            'url' => $this->getPostUrl(),
        ];
    }

    /**
     * Get the notification message
     */
    private function getMessage(): string
    {
        return "{$this->author->name} posted in {$this->group->name}: {$this->post->title}";
    }

    /**
     * Get post content preview (first 150 characters)
     */
    private function getPostPreview(): string
    {
        return \Illuminate\Support\Str::limit($this->post->content, 150);
    }

    /**
     * Get the post URL
     */
    private function getPostUrl(): string
    {
        return route('groups.show', $this->group->slug) . '#post-' . $this->post->id;
    }
}
