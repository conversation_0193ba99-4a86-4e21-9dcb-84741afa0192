<?php

use App\Models\Group;
use App\Models\Organization;
use App\Models\User;

test('user can create group', function () {
    $user = User::factory()->create();
    $organization = Organization::factory()->create();

    $response = $this->actingAs($user)->post('/groups', [
        'name' => 'Test Group',
        'description' => 'A test group',
        'organization_id' => $organization->id,
        'visibility' => 'public',
        'post_approval' => 'none',
        'allow_file_sharing' => true,
        'max_file_size_mb' => 10,
    ]);

    $response->assertRedirect();
    $this->assertDatabaseHas('groups', [
        'name' => 'Test Group',
        'created_by' => $user->id,
    ]);
});

test('user can join public group', function () {
    $creator = User::factory()->create();
    $user = User::factory()->create();

    $group = Group::factory()->create([
        'created_by' => $creator->id,
        'visibility' => 'public'
    ]);

    $response = $this->actingAs($user)->post("/groups/{$group->slug}/join");

    $response->assertRedirect();
    expect($group->hasActiveMember($user))->toBeTrue();
});

test('user can request to join private group', function () {
    $creator = User::factory()->create();
    $user = User::factory()->create();

    $group = Group::factory()->create([
        'created_by' => $creator->id,
        'visibility' => 'private'
    ]);

    $response = $this->actingAs($user)->post("/groups/{$group->slug}/join");

    $response->assertRedirect();
    expect($group->hasMember($user))->toBeTrue();

    $membership = $group->members()->where('user_id', $user->id)->first();
    expect($membership->pivot->status)->toBe('pending');
});

test('group creator cannot leave group', function () {
    $creator = User::factory()->create();

    $group = Group::factory()->create([
        'created_by' => $creator->id,
    ]);

    $response = $this->actingAs($creator)
        ->delete("/groups/{$group->slug}/leave");

    $response->assertRedirect();
    $response->assertSessionHas('error');
});

test('joined_at field is properly cast as Carbon date', function () {
    $user = User::factory()->create();
    $group = Group::factory()->create([
        'created_by' => $user->id,
        'visibility' => 'public'
    ]);

    // Add user as member with joined_at timestamp
    $group->members()->attach($user->id, [
        'role' => 'admin',
        'status' => 'active',
        'joined_at' => now()
    ]);

    // Retrieve the user's groups
    $userGroups = $user->activeGroups()->get();
    $userGroup = $userGroups->first();

    // Check that joined_at is a Carbon instance and diffForHumans works
    expect($userGroup->pivot->joined_at)->toBeInstanceOf(\Carbon\Carbon::class);
    expect($userGroup->pivot->joined_at->diffForHumans())->toBeString();
});

test('my groups page loads without errors', function () {
    $user = User::factory()->create();
    $group = Group::factory()->create([
        'created_by' => $user->id,
        'visibility' => 'public'
    ]);

    // Add user as member with joined_at timestamp
    $group->members()->attach($user->id, [
        'role' => 'admin',
        'status' => 'active',
        'joined_at' => now()
    ]);

    $response = $this->actingAs($user)->get('/groups/my');

    $response->assertStatus(200);
    $response->assertSee($group->name);
    $response->assertSee('ago'); // Should show "X time ago" from diffForHumans()
});

test('group membership livewire component renders correctly', function () {
    $creator = User::factory()->create();
    $user = User::factory()->create();

    $group = Group::factory()->create([
        'created_by' => $creator->id,
        'visibility' => 'public'
    ]);

    // Add creator as admin
    $group->members()->attach($creator->id, [
        'role' => 'admin',
        'status' => 'active',
        'joined_at' => now()
    ]);

    // Add user as member (not creator)
    $group->members()->attach($user->id, [
        'role' => 'member',
        'status' => 'active',
        'joined_at' => now()
    ]);

    $response = $this->actingAs($user)->get('/groups/my');

    $response->assertStatus(200);
    // Check that Livewire component is present (should show Leave button for non-creators)
    $response->assertSee('wire:click');
    // Check that the component doesn't have Alpine.js conflicts
    $response->assertDontSee(':disabled="$isLoading"');
});

test('post approval livewire component renders without alpine conflicts', function () {
    $creator = User::factory()->create();
    $group = Group::factory()->create([
        'created_by' => $creator->id,
        'visibility' => 'public',
        'post_approval' => 'required'
    ]);

    // Add creator as admin
    $group->members()->attach($creator->id, [
        'role' => 'admin',
        'status' => 'active',
        'joined_at' => now()
    ]);

    // Create a pending post
    $post = \App\Models\Post::factory()->create([
        'user_id' => $creator->id,
        'group_id' => $group->id,
        'approval_status' => 'pending',
        'status' => 'published'
    ]);

    $response = $this->actingAs($creator)->get("/groups/{$group->slug}/pending-posts");

    $response->assertStatus(200);
    // Check that the page contains the approval buttons
    $response->assertSee('Approve');
    $response->assertSee('Reject');
    // Most importantly, check that the component doesn't have Alpine.js conflicts
    $response->assertDontSee(':disabled="$isLoading"');
});

test('pending group posts are not shown in main feed', function () {
    $user = User::factory()->create();
    $admin = User::factory()->create();

    // Create a group that requires post approval
    $group = Group::factory()->create([
        'created_by' => $admin->id,
        'visibility' => 'public',
        'post_approval' => 'required'
    ]);

    // Add admin and user as members
    $group->members()->attach($admin->id, [
        'role' => 'admin',
        'status' => 'active',
        'joined_at' => now()
    ]);

    $group->members()->attach($user->id, [
        'role' => 'member',
        'status' => 'active',
        'joined_at' => now()
    ]);

    // Create a pending post (should not appear in feed)
    $pendingPost = \App\Models\Post::factory()->create([
        'user_id' => $user->id,
        'group_id' => $group->id,
        'approval_status' => 'pending',
        'status' => 'published',
        'title' => 'Pending Post Title',
        'content' => 'This post is pending approval'
    ]);

    // Create an approved post (should appear in feed)
    $approvedPost = \App\Models\Post::factory()->create([
        'user_id' => $user->id,
        'group_id' => $group->id,
        'approval_status' => 'approved',
        'status' => 'published',
        'title' => 'Approved Post Title',
        'content' => 'This post is approved'
    ]);

    // Create a personal post (should appear in feed)
    $personalPost = \App\Models\Post::factory()->create([
        'user_id' => $user->id,
        'group_id' => null,
        'organization_id' => null,
        'approval_status' => 'approved',
        'status' => 'published',
        'title' => 'Personal Post Title',
        'content' => 'This is a personal post'
    ]);

    // Check dashboard feed
    $response = $this->actingAs($user)->get('/dashboard');

    $response->assertStatus(200);
    // Should see approved and personal posts
    $response->assertSee('Approved Post Title');
    $response->assertSee('Personal Post Title');
    // Should NOT see pending post
    $response->assertDontSee('Pending Post Title');
    $response->assertDontSee('This post is pending approval');
});

test('approved group posts become visible in main feed', function () {
    $user = User::factory()->create();
    $admin = User::factory()->create();

    // Create a group that requires post approval
    $group = Group::factory()->create([
        'created_by' => $admin->id,
        'visibility' => 'public',
        'post_approval' => 'required'
    ]);

    // Add admin and user as members
    $group->members()->attach($admin->id, [
        'role' => 'admin',
        'status' => 'active',
        'joined_at' => now()
    ]);

    $group->members()->attach($user->id, [
        'role' => 'member',
        'status' => 'active',
        'joined_at' => now()
    ]);

    // Create a pending post
    $post = \App\Models\Post::factory()->create([
        'user_id' => $user->id,
        'group_id' => $group->id,
        'approval_status' => 'pending',
        'status' => 'published',
        'title' => 'Test Post for Approval',
        'content' => 'This post will be approved'
    ]);

    // Initially, post should not be visible in feed
    $response = $this->actingAs($user)->get('/dashboard');
    $response->assertDontSee('Test Post for Approval');

    // Admin approves the post
    $post->update([
        'approval_status' => 'approved',
        'approved_at' => now(),
        'approved_by' => $admin->id,
    ]);

    // Now post should be visible in feed
    $response = $this->actingAs($user)->get('/dashboard');
    $response->assertStatus(200);
    $response->assertSee('Test Post for Approval');
    $response->assertSee('This post will be approved');
});

test('group moderators can see pending posts in pending posts page', function () {
    $admin = User::factory()->create();
    $user = User::factory()->create();

    // Create a group that requires post approval
    $group = Group::factory()->create([
        'created_by' => $admin->id,
        'visibility' => 'public',
        'post_approval' => 'required'
    ]);

    // Add admin and user as members
    $group->members()->attach($admin->id, [
        'role' => 'admin',
        'status' => 'active',
        'joined_at' => now()
    ]);

    $group->members()->attach($user->id, [
        'role' => 'member',
        'status' => 'active',
        'joined_at' => now()
    ]);

    // Create a pending post
    $post = \App\Models\Post::factory()->create([
        'user_id' => $user->id,
        'group_id' => $group->id,
        'approval_status' => 'pending',
        'status' => 'published',
        'title' => 'Pending Post for Moderation',
        'content' => 'This post needs admin approval'
    ]);

    // Admin should see the pending post in the pending posts page
    $response = $this->actingAs($admin)->get("/groups/{$group->slug}/pending-posts");
    $response->assertStatus(200);
    $response->assertSee('Pending Post for Moderation');
    $response->assertSee('This post needs admin approval');

    // Regular user should not have access to pending posts page
    $response = $this->actingAs($user)->get("/groups/{$group->slug}/pending-posts");
    $response->assertStatus(403); // Forbidden
});
