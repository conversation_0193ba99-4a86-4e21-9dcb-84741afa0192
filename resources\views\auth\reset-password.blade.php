<x-guest-layout>
    <!-- <PERSON> Header -->
    <div class="text-center mb-8">
        <h2 class="text-3xl font-bold text-custom-darkest">Set New Password</h2>
        <p class="mt-2 text-sm text-custom-dark-gray">Enter your new password below</p>
    </div>

    <form method="POST" action="{{ route('password.store') }}" class="space-y-6">
        @csrf

        <!-- Password Reset Token -->
        <input type="hidden" name="token" value="{{ $request->route('token') }}">

        <!-- Email Address -->
        <div>
            <x-input-label for="email" :value="__('Email Address')" />
            <x-text-input id="email" type="email" name="email" :value="old('email', $request->email)" required autofocus autocomplete="username" placeholder="Your email address" />
            <x-input-error :messages="$errors->get('email')" class="mt-2" />
        </div>

        <!-- Password -->
        <div>
            <x-input-label for="password" :value="__('New Password')" />
            <x-text-input id="password" type="password" name="password" required autocomplete="new-password" placeholder="Enter your new password" />
            <x-input-error :messages="$errors->get('password')" class="mt-2" />
        </div>

        <!-- Confirm Password -->
        <div>
            <x-input-label for="password_confirmation" :value="__('Confirm New Password')" />
            <x-text-input id="password_confirmation" type="password" name="password_confirmation" required autocomplete="new-password" placeholder="Confirm your new password" />
            <x-input-error :messages="$errors->get('password_confirmation')" class="mt-2" />
        </div>

        <!-- Password Requirements -->
        <div class="bg-custom-lightest rounded-lg p-4">
            <p class="text-xs text-custom-dark-gray mb-2 font-medium">Password requirements:</p>
            <ul class="text-xs text-custom-dark-gray space-y-1">
                <li>• At least 8 characters long</li>
                <li>• Include uppercase and lowercase letters</li>
                <li>• Include at least one number</li>
                <li>• Include at least one special character</li>
            </ul>
        </div>

        <!-- Submit Button -->
        <div>
            <x-primary-button class="w-full">
                {{ __('Reset Password') }}
            </x-primary-button>
        </div>

        <!-- Back to Login -->
        <div class="text-center pt-4 border-t border-custom-lightest">
            <p class="text-sm text-custom-dark-gray">
                Remember your password?
                <a href="{{ route('login') }}" class="font-medium text-custom-green hover:text-green-600 transition-colors duration-200">
                    Back to login
                </a>
            </p>
        </div>
    </form>
</x-guest-layout>
