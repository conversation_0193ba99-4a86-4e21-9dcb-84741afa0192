<?php

namespace App\Events;

use App\Models\User;
use App\Models\Post;
use App\Models\Share;

use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class PostSharedEvent
{
    use Dispatchable, SerializesModels;

    public $user;
    public $post;
    public $share;

    /**
     * Create a new event instance.
     */
    public function __construct(User $user, Post $post, Share $share)
    {
        $this->user = $user;
        $this->post = $post;
        $this->share = $share;
    }


}
