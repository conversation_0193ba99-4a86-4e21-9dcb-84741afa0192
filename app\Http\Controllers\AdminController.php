<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Post;
use App\Models\Organization;
use App\Models\Group;
use App\Models\Scholarship;
use App\Models\Comment;
use App\Models\Reaction;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class AdminController extends Controller
{
    /**
     * Display the admin dashboard
     */
    public function dashboard()
    {
        // Get basic statistics
        $stats = [
            'total_users' => User::count(),
            'total_posts' => Post::count(),
            'total_organizations' => Organization::count(),
            'total_groups' => Group::count(),
            'total_scholarships' => Scholarship::count(),
            'total_comments' => Comment::count(),
            'total_reactions' => Reaction::count(),
        ];

        // Get recent activities (last 10 activities)
        $recent_activities = $this->getRecentActivities();

        return view('admin.dashboard', compact('stats', 'recent_activities'));
    }

    /**
     * Display users management page
     */
    public function users(Request $request)
    {
        $query = User::query();

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('student_id', 'like', "%{$search}%");
            });
        }

        // Filter by role
        if ($request->filled('role')) {
            $query->where('role', $request->role);
        }

        // Filter by verification status
        if ($request->filled('verified')) {
            if ($request->verified === 'verified') {
                $query->whereNotNull('email_verified_at');
            } else {
                $query->whereNull('email_verified_at');
            }
        }

        $users = $query->withCount(['posts', 'comments', 'organizations', 'groups'])
                      ->orderBy('created_at', 'desc')
                      ->paginate(20);

        $user_stats = [
            'total' => User::count(),
            'students' => User::where('role', 'student')->count(),
            'org_officers' => User::where('role', 'org_officer')->count(),
            'admins' => User::where('role', 'admin')->count(),
            'verified' => User::whereNotNull('email_verified_at')->count(),
            'unverified' => User::whereNull('email_verified_at')->count(),
        ];

        return view('admin.users.index', compact('users', 'user_stats'));
    }

    /**
     * Display posts management page
     */
    public function posts(Request $request)
    {
        $query = Post::with(['user', 'organization', 'group']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('content', 'like', "%{$search}%");
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by approval status
        if ($request->filled('approval_status')) {
            $query->where('approval_status', $request->approval_status);
        }

        // Filter by type
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        $posts = $query->withCount(['comments', 'reactions'])
                      ->orderBy('created_at', 'desc')
                      ->paginate(20);

        $post_stats = [
            'total' => Post::count(),
            'published' => Post::where('status', 'published')->count(),
            'draft' => Post::where('status', 'draft')->count(),
            'pending_approval' => Post::where('approval_status', 'pending')->count(),
            'approved' => Post::where('approval_status', 'approved')->count(),
            'rejected' => Post::where('approval_status', 'rejected')->count(),
        ];

        return view('admin.posts.index', compact('posts', 'post_stats'));
    }

    /**
     * Display organizations management page
     */
    public function organizations(Request $request)
    {
        $query = Organization::with(['creator']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by page mode (instead of type since there's no type column)
        if ($request->filled('mode')) {
            if ($request->mode === 'page') {
                $query->where('is_page_mode', true);
            } elseif ($request->mode === 'organization') {
                $query->where('is_page_mode', false);
            }
        }

        $organizations = $query->withCount(['members', 'posts'])
                              ->orderBy('created_at', 'desc')
                              ->paginate(20);

        $org_stats = [
            'total' => Organization::count(),
            'active' => Organization::where('status', 'active')->count(),
            'inactive' => Organization::where('status', 'inactive')->count(),
            'pending' => Organization::where('status', 'pending')->count(),
        ];

        return view('admin.organizations.index', compact('organizations', 'org_stats'));
    }

    /**
     * Display groups management page
     */
    public function groups(Request $request)
    {
        $query = Group::with(['creator']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Filter by visibility (not privacy)
        if ($request->filled('visibility')) {
            $query->where('visibility', $request->visibility);
        }

        $groups = $query->withCount(['members', 'posts'])
                       ->orderBy('created_at', 'desc')
                       ->paginate(20);

        $group_stats = [
            'total' => Group::count(),
            'public' => Group::where('visibility', 'public')->count(),
            'private' => Group::where('visibility', 'private')->count(),
        ];

        return view('admin.groups.index', compact('groups', 'group_stats'));
    }

    /**
     * Display scholarships management page
     */
    public function scholarships(Request $request)
    {
        $query = Scholarship::with(['creator']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('provider', 'like', "%{$search}%");
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        $scholarships = $query->orderBy('created_at', 'desc')
                             ->paginate(20);

        $scholarship_stats = [
            'total' => Scholarship::count(),
            'active' => Scholarship::where('status', 'active')->count(),
            'inactive' => Scholarship::where('status', 'inactive')->count(),
            'expired' => Scholarship::where('deadline', '<', now())->count(),
        ];

        return view('admin.scholarships.index', compact('scholarships', 'scholarship_stats'));
    }

    /**
     * Display analytics page
     */
    public function analytics()
    {
        // Get user registration trends (last 30 days)
        $user_trends = User::select(
            DB::raw('DATE(created_at) as date'),
            DB::raw('COUNT(*) as count')
        )
        ->where('created_at', '>=', Carbon::now()->subDays(30))
        ->groupBy('date')
        ->orderBy('date')
        ->get();

        // Get post creation trends (last 30 days)
        $post_trends = Post::select(
            DB::raw('DATE(created_at) as date'),
            DB::raw('COUNT(*) as count')
        )
        ->where('created_at', '>=', Carbon::now()->subDays(30))
        ->groupBy('date')
        ->orderBy('date')
        ->get();

        // Get most active users
        $active_users = User::withCount(['posts', 'comments'])
                           ->orderByDesc('posts_count')
                           ->limit(10)
                           ->get();

        // Get popular posts
        $popular_posts = Post::withCount(['reactions', 'comments'])
                            ->orderByDesc('reactions_count')
                            ->limit(10)
                            ->get();

        return view('admin.analytics', compact(
            'user_trends',
            'post_trends',
            'active_users',
            'popular_posts'
        ));
    }

    /**
     * Get recent activities for dashboard
     */
    private function getRecentActivities()
    {
        $activities = [];

        // Recent user registrations
        $recent_users = User::latest()->limit(3)->get();
        foreach ($recent_users as $user) {
            $activities[] = [
                'description' => "New user registered: {$user->name}",
                'time' => $user->created_at->diffForHumans(),
                'type' => 'user_registration'
            ];
        }

        // Recent posts
        $recent_posts = Post::with('user')->latest()->limit(3)->get();
        foreach ($recent_posts as $post) {
            $activities[] = [
                'description' => "New post created by {$post->user->name}: " . \Str::limit($post->title, 50),
                'time' => $post->created_at->diffForHumans(),
                'type' => 'post_created'
            ];
        }

        // Sort by time and limit to 10
        usort($activities, function ($a, $b) {
            return strtotime($b['time']) - strtotime($a['time']);
        });

        return array_slice($activities, 0, 10);
    }
}
