<?php

namespace App\Notifications;

use App\Models\User;
use App\Models\Share;
use App\Models\Reaction;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\BroadcastMessage;
use Illuminate\Notifications\Notification;

class ShareReacted extends Notification implements ShouldQueue
{
    use Queueable;

    public $user;
    public $share;
    public $reaction;

    /**
     * Create a new notification instance.
     */
    public function __construct(User $user, Share $share, Reaction $reaction)
    {
        $this->user = $user;
        $this->share = $share;
        $this->reaction = $reaction;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['database', 'broadcast'];
    }

    /**
     * Get the broadcastable representation of the notification.
     */
    public function toBroadcast(object $notifiable): BroadcastMessage
    {
        return new BroadcastMessage([
            'id' => $this->id,
            'type' => 'share_reacted',
            'user_id' => $this->user->id,
            'user_name' => $this->user->name,
            'user_avatar' => $this->user->getNotificationAvatarUrl(),
            'share_id' => $this->share->id,
            'post_id' => $this->share->post->id,
            'post_title' => $this->share->post->title,
            'reaction_type' => $this->reaction->type,
            'reaction_emoji' => $this->getReactionEmoji($this->reaction->type),
            'message' => $this->getMessage(),
            'url' => $this->getShareUrl(),
            'created_at' => now()->toISOString(),
        ]);
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'type' => 'share_reacted',
            'user_id' => $this->user->id,
            'user_name' => $this->user->name,
            'user_avatar' => $this->user->getNotificationAvatarUrl(),
            'share_id' => $this->share->id,
            'post_id' => $this->share->post->id,
            'post_title' => $this->share->post->title,
            'reaction_type' => $this->reaction->type,
            'reaction_emoji' => $this->getReactionEmoji($this->reaction->type),
            'message' => $this->getMessage(),
            'url' => $this->getShareUrl(),
        ];
    }

    /**
     * Get the notification message.
     */
    private function getMessage(): string
    {
        $reactionLabel = $this->getReactionLabel($this->reaction->type);
        return "{$this->user->name} {$reactionLabel} your shared post";
    }

    /**
     * Get the share URL.
     */
    private function getShareUrl(): string
    {
        // For now, redirect to the original post
        // In the future, you might want to create a dedicated share view
        return route('posts.show', $this->share->post->id);
    }

    /**
     * Get reaction emoji for the given type.
     */
    private function getReactionEmoji(string $type): string
    {
        $reactions = [
            'like' => '👍',
            'love' => '❤️',
            'haha' => '😂',
            'wow' => '😮',
            'sad' => '😢',
            'angry' => '😠',
        ];

        return $reactions[$type] ?? '👍';
    }

    /**
     * Get reaction label for the given type.
     */
    private function getReactionLabel(string $type): string
    {
        $labels = [
            'like' => 'liked',
            'love' => 'loved',
            'haha' => 'laughed at',
            'wow' => 'was amazed by',
            'sad' => 'was saddened by',
            'angry' => 'was angered by',
        ];

        return $labels[$type] ?? 'reacted to';
    }
}
