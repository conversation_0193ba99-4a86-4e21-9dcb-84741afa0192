/**
 * Simple test script to verify notification functionality
 * This can be run in the browser console to test notifications
 */

function testNotificationSystem() {
    console.log('Testing UniLink Notification System...');
    
    // Check if notifications are supported
    if (!('Notification' in window)) {
        console.error('This browser does not support notifications');
        return;
    }
    
    console.log('Current notification permission:', Notification.permission);
    
    // Test notification permission request
    if (Notification.permission === 'default') {
        console.log('Requesting notification permission...');
        Notification.requestPermission().then(permission => {
            console.log('Permission result:', permission);
            if (permission === 'granted') {
                testBrowserNotification();
            }
        });
    } else if (Notification.permission === 'granted') {
        testBrowserNotification();
    } else {
        console.log('Notifications are blocked by the user');
    }
}

function testBrowserNotification() {
    console.log('Testing browser notification...');
    
    const notification = new Notification('UniLink Test', {
        body: 'This is a test notification from UniLink!',
        icon: 'https://ui-avatars.com/api/?name=UniLink&color=7BC74D&background=EEEEEE&size=64',
        tag: 'test-notification'
    });
    
    notification.onclick = function() {
        console.log('Test notification clicked');
        window.focus();
        notification.close();
    };
    
    // Auto-close after 5 seconds
    setTimeout(() => {
        notification.close();
    }, 5000);
}

function testNotificationAPI() {
    console.log('Testing notification API endpoints...');
    
    // Test getting notifications
    fetch('/notifications', {
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        console.log('Notifications API response:', data);
    })
    .catch(error => {
        console.error('Error testing notifications API:', error);
    });
    
    // Test unread count
    fetch('/notifications/unread-count', {
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        console.log('Unread count API response:', data);
    })
    .catch(error => {
        console.error('Error testing unread count API:', error);
    });
}

function testEchoConnection() {
    console.log('Testing Laravel Echo connection...');
    
    if (window.Echo) {
        console.log('Echo is available');
        
        // Test private channel connection
        const userId = document.querySelector('meta[name="user-id"]')?.getAttribute('content');
        if (userId) {
            const channel = window.Echo.private(`App.Models.User.${userId}`);
            console.log('Connected to private channel:', channel);
            
            channel.notification((notification) => {
                console.log('Received real-time notification:', notification);
            });
        } else {
            console.log('User ID not found in meta tags');
        }
    } else {
        console.error('Laravel Echo is not available');
    }
}

// Export functions for console use
window.testNotificationSystem = testNotificationSystem;
window.testBrowserNotification = testBrowserNotification;
window.testNotificationAPI = testNotificationAPI;
window.testEchoConnection = testEchoConnection;

console.log('Notification test functions loaded. Available functions:');
console.log('- testNotificationSystem()');
console.log('- testBrowserNotification()');
console.log('- testNotificationAPI()');
console.log('- testEchoConnection()');
