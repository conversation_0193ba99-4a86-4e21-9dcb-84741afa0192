<x-unilink-layout>
    <!-- Success Messages -->
    @if (session('status') === 'profile-updated')
        <div class="mb-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg" x-data="{ show: true }" x-show="show" x-transition x-init="setTimeout(() => show = false, 5000)">
            <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                </svg>
                Profile updated successfully!
            </div>
        </div>
    @endif

    @if (session('status') === 'password-updated')
        <div class="mb-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg" x-data="{ show: true }" x-show="show" x-transition x-init="setTimeout(() => show = false, 5000)">
            <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                </svg>
                Password updated successfully!
            </div>
        </div>
    @endif

    @if (session('status') === 'background-photo-deleted')
        <div class="mb-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg" x-data="{ show: true }" x-show="show" x-transition x-init="setTimeout(() => show = false, 5000)">
            <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                </svg>
                Background photo removed successfully!
            </div>
        </div>
    @endif

    <!-- Profile Header Section -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden mb-6">
        <!-- Cover Photo -->
        <div class="h-48 sm:h-64 profile-cover relative"
             @if($user->background_photo)
                style="background-image: url('{{ \Illuminate\Support\Facades\Storage::disk('public')->url($user->background_photo) }}'); background-size: cover; background-position: center;"
             @endif>
            <div class="absolute inset-0 bg-gradient-to-br from-black/10 to-black/30"></div>
            <!-- Cover photo management buttons (if own profile) -->
            @if(auth()->id() === $user->id)
                <div class="absolute bottom-4 right-4 flex space-x-2">
                    <!-- Upload/Edit Background Photo Button -->
                    <button onclick="document.getElementById('backgroundPhotoInput').click()"
                            class="bg-green-500/90 hover:bg-custom-green text-white px-3 py-2 rounded-lg text-sm font-medium transition-all shadow-sm hover:shadow-md">
                        <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                        </svg>
                        <span class="hidden sm:inline">{{ $user->background_photo ? 'Edit Cover' : 'Add Cover' }}</span>
                    </button>

                    <!-- Delete Background Photo Button (only show if photo exists) -->
                    @if($user->background_photo)
                        <button onclick="deleteBackgroundPhoto()"
                                class="bg-red-500/90 hover:bg-red-500 text-white px-3 py-2 rounded-lg text-sm font-medium transition-all shadow-sm hover:shadow-md">
                            <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                            </svg>
                            <span class="hidden sm:inline">Remove</span>
                        </button>
                    @endif
                </div>

                <!-- Hidden file input for background photo -->
                <form id="backgroundPhotoForm" action="{{ route('profile.update') }}" method="POST" enctype="multipart/form-data" style="display: none;">
                    @csrf
                    @method('PATCH')
                    <input type="file" id="backgroundPhotoInput" name="background_photo" accept="image/*" onchange="uploadBackgroundPhoto()">
                </form>
            @endif
        </div>

        <!-- Profile Info -->
        <div class="px-4 sm:px-6 pb-6">
            <div class="flex flex-col sm:flex-row sm:items-end sm:space-x-6">
                <!-- Profile Picture -->
                <div class="relative -mt-12 sm:-mt-16 mb-4 sm:mb-0 flex-shrink-0">
                    <img class="w-24 h-24 sm:w-32 sm:h-32 rounded-full profile-avatar-border"
                         src="{{ $user->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url($user->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($user->name) . '&color=7BC74D&background=EEEEEE&size=128' }}"
                         alt="{{ $user->name }}">
                    @if(auth()->id() === $user->id)
                        <button class="absolute bottom-1 right-1 sm:bottom-2 sm:right-2 bg-white hover:bg-gray-50 rounded-full p-1.5 sm:p-2 shadow-lg transition-all hover:shadow-xl border border-gray-200">
                            <svg class="w-3 h-3 sm:w-4 sm:h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                            </svg>
                        </button>
                    @endif
                </div>

                <!-- User Info -->
                <div class="flex-1 min-w-0">
                    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                        <div class="flex-1 min-w-0">
                            <h1 class="text-2xl sm:text-3xl font-bold text-gray-900 truncate">{{ $user->name }}</h1>
                            @if($user->bio)
                                <p class="text-gray-600 mt-1 text-sm sm:text-base line-clamp-2">{{ $user->bio }}</p>
                            @endif
                            <div class="flex flex-wrap items-center gap-x-4 gap-y-1 mt-2 text-xs sm:text-sm text-gray-500">
                                @if($user->student_id)
                                    <span class="flex items-center">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V8a2 2 0 00-2-2h-5m-4 0V5a2 2 0 114 0v1m-4 0a2 2 0 104 0m-5 8a2 2 0 100-4 2 2 0 000 4zm0 0c1.306 0 2.417.835 2.83 2M9 14a3.001 3.001 0 00-2.83 2M15 11h3m-3 4h2" />
                                        </svg>
                                        ID: {{ $user->student_id }}
                                    </span>
                                @endif
                                <span class="flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                    </svg>
                                    {{ ucfirst($user->role) }}
                                </span>
                                <span class="flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 0h6m-6 0l-2 13a2 2 0 002 2h6a2 2 0 002-2L14 7" />
                                    </svg>
                                    Joined {{ $user->created_at->format('M Y') }}
                                </span>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="flex space-x-2 mt-4 sm:mt-0 sm:ml-4">
                            @if(auth()->id() !== $user->id)
                                <button class="bg-custom-green text-white px-4 py-2 rounded-lg hover:bg-opacity-90 transition-all font-medium text-sm shadow-sm hover:shadow-md">
                                    <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                                    </svg>
                                    Message
                                </button>
                                <livewire:user-follower :user="$user" />
                            @else
                                <a href="{{ route('profile.edit') }}" class="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-all font-medium text-sm shadow-sm hover:shadow-md">
                                    <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                    </svg>
                                    Edit Profile
                                </a>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <!-- Stats -->
            <div class="grid grid-cols-3 sm:grid-cols-7 gap-4 mt-6 pt-6 border-t border-gray-200">
                <div class="text-center hover:bg-gray-50 rounded-lg p-2 transition-colors cursor-pointer">
                    <div class="text-xl sm:text-2xl font-bold text-gray-900">{{ $stats['posts_count'] }}</div>
                    <div class="text-xs sm:text-sm text-gray-500">Posts</div>
                </div>
                @if(auth()->id() === $user->id)
                    <a href="{{ route('follow-management.followers') }}" class="text-center hover:bg-gray-50 rounded-lg p-2 transition-colors cursor-pointer">
                        <div class="text-xl sm:text-2xl font-bold text-gray-900">{{ $user->followers()->count() }}</div>
                        <div class="text-xs sm:text-sm text-gray-500">Followers</div>
                    </a>
                    <a href="{{ route('follow-management.following') }}" class="text-center hover:bg-gray-50 rounded-lg p-2 transition-colors cursor-pointer">
                        <div class="text-xl sm:text-2xl font-bold text-gray-900">{{ $user->following()->count() }}</div>
                        <div class="text-xs sm:text-sm text-gray-500">Following</div>
                    </a>
                @else
                    <a href="{{ route('users.followers', $user) }}" class="text-center hover:bg-gray-50 rounded-lg p-2 transition-colors cursor-pointer">
                        <div class="text-xl sm:text-2xl font-bold text-gray-900">{{ $user->followers()->count() }}</div>
                        <div class="text-xs sm:text-sm text-gray-500">Followers</div>
                    </a>
                    <a href="{{ route('users.following', $user) }}" class="text-center hover:bg-gray-50 rounded-lg p-2 transition-colors cursor-pointer">
                        <div class="text-xl sm:text-2xl font-bold text-gray-900">{{ $user->following()->count() }}</div>
                        <div class="text-xs sm:text-sm text-gray-500">Following</div>
                    </a>
                @endif
                <div class="text-center hover:bg-gray-50 rounded-lg p-2 transition-colors cursor-pointer">
                    <div class="text-xl sm:text-2xl font-bold text-gray-900">{{ $stats['organizations_count'] }}</div>
                    <div class="text-xs sm:text-sm text-gray-500">Organizations</div>
                </div>
                <div class="text-center hover:bg-gray-50 rounded-lg p-2 transition-colors cursor-pointer">
                    <div class="text-xl sm:text-2xl font-bold text-gray-900">{{ $stats['created_organizations_count'] }}</div>
                    <div class="text-xs sm:text-sm text-gray-500">Created</div>
                </div>
                <div class="text-center hover:bg-gray-50 rounded-lg p-2 transition-colors cursor-pointer">
                    <div class="text-xl sm:text-2xl font-bold text-gray-900">{{ $stats['total_likes'] }}</div>
                    <div class="text-xs sm:text-sm text-gray-500">Likes</div>
                </div>
                <div class="text-center hover:bg-gray-50 rounded-lg p-2 transition-colors cursor-pointer">
                    <div class="text-xl sm:text-2xl font-bold text-gray-900">{{ $stats['total_comments'] }}</div>
                    <div class="text-xs sm:text-sm text-gray-500">Comments</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content Area -->
    <div class="profile-container profile-responsive-grid">
        <!-- Left Column - About & Profile Management -->
        <div class="space-y-6">
            <!-- About Section -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-lg font-semibold text-gray-900">About</h2>
                    @if(auth()->id() === $user->id)
                        <button class="text-gray-400 hover:text-gray-600 transition-colors">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                            </svg>
                        </button>
                    @endif
                </div>
                <div class="space-y-4">
                    @if($user->email)
                        <div class="flex items-start space-x-3">
                            <div class="flex-shrink-0 w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center">
                                <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                </svg>
                            </div>
                            <div class="flex-1 min-w-0">
                                <p class="text-sm text-gray-500">Email</p>
                                <p class="text-sm font-medium text-gray-900 break-all">{{ $user->email }}</p>
                            </div>
                        </div>
                    @endif
                    @if($user->phone)
                        <div class="flex items-start space-x-3">
                            <div class="flex-shrink-0 w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center">
                                <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                                </svg>
                            </div>
                            <div class="flex-1 min-w-0">
                                <p class="text-sm text-gray-500">Phone</p>
                                <p class="text-sm font-medium text-gray-900">{{ $user->phone }}</p>
                            </div>
                        </div>
                    @endif
                    <div class="flex items-start space-x-3">
                        <div class="flex-shrink-0 w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center">
                            <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                            </svg>
                        </div>
                        <div class="flex-1 min-w-0">
                            <p class="text-sm text-gray-500">Role</p>
                            <p class="text-sm font-medium text-gray-900">{{ ucfirst($user->role) }}</p>
                        </div>
                    </div>
                    <div class="flex items-start space-x-3">
                        <div class="flex-shrink-0 w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center">
                            <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 0h6m-6 0l-2 13a2 2 0 002 2h6a2 2 0 002-2L14 7" />
                            </svg>
                        </div>
                        <div class="flex-1 min-w-0">
                            <p class="text-sm text-gray-500">Member since</p>
                            <p class="text-sm font-medium text-gray-900">{{ $user->created_at->format('F Y') }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions (Own Profile Only) -->
            @if(auth()->id() === $user->id)
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
                    <div class="space-y-3">
                        <button onclick="openCreatePostModal()" class="w-full flex items-center space-x-3 p-3 text-left hover:bg-gray-50 rounded-lg transition-colors">
                            <div class="flex-shrink-0 w-8 h-8 bg-custom-green/10 rounded-lg flex items-center justify-center">
                                <svg class="w-4 h-4 text-custom-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                                </svg>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-900">Create Post</p>
                                <p class="text-xs text-gray-500">Share what's on your mind</p>
                            </div>
                        </button>
                        <a href="{{ route('organizations.my') }}" class="w-full flex items-center space-x-3 p-3 text-left hover:bg-gray-50 rounded-lg transition-colors">
                            <div class="flex-shrink-0 w-8 h-8 bg-blue-50 rounded-lg flex items-center justify-center">
                                <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                                </svg>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-900">My Organizations</p>
                                <p class="text-xs text-gray-500">Manage your memberships</p>
                            </div>
                        </a>
                        <a href="{{ route('groups.my') }}" class="w-full flex items-center space-x-3 p-3 text-left hover:bg-gray-50 rounded-lg transition-colors">
                            <div class="flex-shrink-0 w-8 h-8 bg-purple-50 rounded-lg flex items-center justify-center">
                                <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                                </svg>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-900">My Groups</p>
                                <p class="text-xs text-gray-500">View your groups</p>
                            </div>
                        </a>
                    </div>
                </div>
            @endif

            <!-- Profile Management (Only for own profile) -->
            @if(auth()->id() === $user->id)
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden" x-data="{ activeTab: 'profile' }">
                    <!-- Tab Navigation -->
                    <div class="border-b border-gray-200">
                        <div class="flex justify-between items-center px-6 py-4">
                            <nav class="flex space-x-8" aria-label="Tabs">
                                <button @click="activeTab = 'profile'" :class="activeTab === 'profile' ? 'border-custom-green text-custom-green' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors">
                                    Profile Info
                                </button>
                                <button @click="activeTab = 'password'" :class="activeTab === 'password' ? 'border-custom-green text-custom-green' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors">
                                    Password
                                </button>
                                <button @click="activeTab = 'danger'" :class="activeTab === 'danger' ? 'border-red-500 text-red-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors">
                                    Danger Zone
                                </button>
                            </nav>
                            <a href="{{ route('profile.edit') }}" class="text-sm text-custom-green hover:text-custom-dark-gray font-medium">
                                Full Edit Page →
                            </a>
                        </div>
                    </div>

                    <!-- Tab Content -->
                    <div class="p-6">
                        <!-- Profile Information Tab -->
                        <div x-show="activeTab === 'profile'" x-transition>
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Update Profile Information</h3>
                            @include('profile.partials.update-profile-information-form')
                        </div>

                        <!-- Password Tab -->
                        <div x-show="activeTab === 'password'" x-transition>
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Update Password</h3>
                            @include('profile.partials.update-password-form')
                        </div>

                        <!-- Danger Zone Tab -->
                        <div x-show="activeTab === 'danger'" x-transition>
                            <h3 class="text-lg font-semibold text-red-600 mb-4">Delete Account</h3>
                            @include('profile.partials.delete-user-form')
                        </div>
                    </div>
                </div>
            @endif

            <!-- Achievements Section -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">Achievements</h3>
                    <span class="px-2 py-1 bg-yellow-100 text-yellow-600 text-xs font-medium rounded-full">{{ $stats['posts_count'] + $stats['organizations_count'] }}</span>
                </div>
                <div class="grid grid-cols-2 gap-3">
                    @if($stats['posts_count'] >= 10)
                        <div class="flex items-center space-x-2 p-2 bg-green-50 rounded-lg">
                            <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                <svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                </svg>
                            </div>
                            <div>
                                <p class="text-xs font-medium text-green-800">Active Poster</p>
                                <p class="text-xs text-green-600">10+ posts</p>
                            </div>
                        </div>
                    @endif

                    @if($stats['organizations_count'] >= 3)
                        <div class="flex items-center space-x-2 p-2 bg-blue-50 rounded-lg">
                            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                <svg class="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z" />
                                </svg>
                            </div>
                            <div>
                                <p class="text-xs font-medium text-blue-800">Community Leader</p>
                                <p class="text-xs text-blue-600">3+ organizations</p>
                            </div>
                        </div>
                    @endif

                    @if($stats['total_likes'] >= 50)
                        <div class="flex items-center space-x-2 p-2 bg-red-50 rounded-lg">
                            <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                                <svg class="w-4 h-4 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div>
                                <p class="text-xs font-medium text-red-800">Well Liked</p>
                                <p class="text-xs text-red-600">50+ likes</p>
                            </div>
                        </div>
                    @endif

                    @if($user->created_at->diffInMonths(now()) >= 12)
                        <div class="flex items-center space-x-2 p-2 bg-purple-50 rounded-lg">
                            <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                                <svg class="w-4 h-4 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div>
                                <p class="text-xs font-medium text-purple-800">Veteran</p>
                                <p class="text-xs text-purple-600">1+ year member</p>
                            </div>
                        </div>
                    @endif
                </div>
                @if($stats['posts_count'] < 10 && $stats['organizations_count'] < 3 && $stats['total_likes'] < 50 && $user->created_at->diffInMonths(now()) < 12)
                    <div class="text-center py-4">
                        <p class="text-sm text-gray-500">Keep engaging to unlock achievements!</p>
                    </div>
                @endif
            </div>

            <!-- Organizations Section -->
            @if($user->activeOrganizations->count() > 0)
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h2 class="text-lg font-semibold text-gray-900">Organizations</h2>
                        <span class="px-2 py-1 bg-gray-100 text-gray-600 text-xs font-medium rounded-full">{{ $user->activeOrganizations->count() }}</span>
                    </div>
                    <div class="space-y-3">
                        @foreach($user->activeOrganizations->take(5) as $organization)
                            <div class="flex items-center space-x-3 p-2 hover:bg-gray-50 rounded-lg transition-colors">
                                <img class="w-10 h-10 rounded-lg object-cover"
                                     src="{{ $organization->logo ? \Illuminate\Support\Facades\Storage::disk('public')->url($organization->logo) : 'https://ui-avatars.com/api/?name=' . urlencode($organization->name) . '&color=3B82F6&background=DBEAFE' }}"
                                     alt="{{ $organization->name }}">
                                <div class="flex-1 min-w-0">
                                    <a href="{{ route('organizations.show', $organization) }}" class="font-medium text-gray-900 hover:text-custom-green truncate block text-sm">
                                        {{ $organization->name }}
                                    </a>
                                    <p class="text-xs text-gray-500">{{ ucfirst($organization->pivot->role) }}</p>
                                </div>
                            </div>
                        @endforeach
                        @if($user->activeOrganizations->count() > 5)
                            <div class="pt-2 border-t border-gray-100">
                                <a href="{{ route('organizations.my') }}" class="text-custom-green hover:text-custom-dark-gray text-sm font-medium flex items-center">
                                    View all organizations
                                    <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                    </svg>
                                </a>
                            </div>
                        @endif
                    </div>
                </div>
            @endif

            <!-- Activity Summary -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Activity Summary</h3>
                <div class="space-y-3">
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                            </div>
                            <span class="text-sm font-medium text-gray-900">Posts Created</span>
                        </div>
                        <span class="text-sm font-bold text-gray-900">{{ $stats['posts_count'] }}</span>
                    </div>
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                                </svg>
                            </div>
                            <span class="text-sm font-medium text-gray-900">Content Shared</span>
                        </div>
                        <span class="text-sm font-bold text-gray-900">{{ $stats['shares_count'] }}</span>
                    </div>
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                                <svg class="w-4 h-4 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <span class="text-sm font-medium text-gray-900">Likes Received</span>
                        </div>
                        <span class="text-sm font-bold text-gray-900">{{ $stats['total_likes'] }}</span>
                    </div>
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                                <svg class="w-4 h-4 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                                </svg>
                            </div>
                            <span class="text-sm font-medium text-gray-900">Comments Made</span>
                        </div>
                        <span class="text-sm font-bold text-gray-900">{{ $stats['total_comments'] }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Column - Posts -->
        <div class="space-y-6">
            <!-- Create Post (if own profile) -->
            @if(auth()->id() === $user->id)
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-4">
                    <div class="flex items-center space-x-3">
                        <img class="h-10 w-10 rounded-full object-cover" src="{{ auth()->user()->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url(auth()->user()->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode(auth()->user()->name) . '&color=7BC74D&background=EEEEEE' }}" alt="{{ auth()->user()->name }}">
                        <div class="flex-1">
                            <button onclick="openCreatePostModal()" class="w-full text-left px-4 py-3 bg-gray-100 hover:bg-gray-200 rounded-full text-gray-600 transition-all placeholder-gray-500">
                                What's on your mind, {{ auth()->user()->name }}?
                            </button>
                        </div>
                    </div>
                    <div class="flex items-center justify-between mt-3 pt-3 border-t border-gray-100">
                        <div class="flex space-x-4">
                            <button onclick="openCreatePostModal()" class="flex items-center space-x-2 text-gray-600 hover:text-custom-green transition-colors">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                </svg>
                                <span class="text-sm font-medium">Photo</span>
                            </button>
                            <button onclick="openCreatePostModal()" class="flex items-center space-x-2 text-gray-600 hover:text-custom-green transition-colors">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.586-6.586a2 2 0 00-2.828-2.828z" />
                                </svg>
                                <span class="text-sm font-medium">File</span>
                            </button>
                        </div>
                        <button onclick="openCreatePostModal()" class="bg-custom-green text-white px-4 py-2 rounded-lg hover:bg-opacity-90 transition-all text-sm font-medium">
                            Post
                        </button>
                    </div>
                </div>
            @endif

            <!-- Activity Filter Tabs -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden" x-data="{ activeFilter: 'all' }">
                <div class="border-b border-gray-200 bg-gray-50">
                    <nav class="flex space-x-8 px-6 py-3" aria-label="Tabs">
                        <button @click="activeFilter = 'all'" :class="activeFilter === 'all' ? 'border-custom-green text-custom-green bg-white' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-3 border-b-2 font-medium text-sm transition-all rounded-t-lg">
                            All Activity
                        </button>
                        <button @click="activeFilter = 'posts'" :class="activeFilter === 'posts' ? 'border-custom-green text-custom-green bg-white' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-3 border-b-2 font-medium text-sm transition-all rounded-t-lg">
                            Posts ({{ $stats['posts_count'] }})
                        </button>
                        <button @click="activeFilter = 'shares'" :class="activeFilter === 'shares' ? 'border-custom-green text-custom-green bg-white' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'" class="whitespace-nowrap py-2 px-3 border-b-2 font-medium text-sm transition-all rounded-t-lg">
                            Shares ({{ $stats['shares_count'] }})
                        </button>
                    </nav>
                </div>

                <!-- Activity Header -->
                <div class="px-6 py-4 border-b border-gray-100">
                    <div class="flex items-center justify-between">
                        <h2 class="text-lg font-semibold text-gray-900">
                            {{ auth()->id() === $user->id ? 'Your Recent Activity' : $user->name . "'s Recent Activity" }}
                        </h2>
                        <div class="text-sm text-gray-500">
                            <span class="font-medium">{{ $stats['total_activity_count'] }}</span> total activities
                        </div>
                    </div>
                </div>

                <!-- Activity Content -->
                <div class="divide-y divide-gray-100">
                    @forelse($recentActivity as $activity)
                        <div class="p-6"
                             x-show="activeFilter === 'all' || (activeFilter === 'posts' && '{{ $activity->type }}' === 'post') || (activeFilter === 'shares' && '{{ $activity->type }}' === 'share')"
                             x-transition>
                            @if($activity->type === 'post')
                                <x-post-card :post="$activity->data" />
                            @elseif($activity->type === 'share')
                                <x-shared-post-card :share="$activity->data" />
                            @endif
                        </div>
                    @empty
                        <div class="p-12 text-center">
                            <svg class="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">No activity yet</h3>
                            <p class="text-gray-500 mb-4">
                                {{ auth()->id() === $user->id ? "You haven't created any posts or shared anything yet." : $user->name . " hasn't shared any posts or content yet." }}
                            </p>
                            @if(auth()->id() === $user->id)
                                <button onclick="openCreatePostModal()" class="bg-custom-green text-white px-6 py-2 rounded-lg hover:bg-opacity-90 transition-all font-medium">
                                    Create Your First Post
                                </button>
                            @endif
                        </div>
                    @endforelse
                </div>
            </div>
        </div>
    </div>

    <!-- Include Create Post Modal if it's the user's own profile -->
    @if(auth()->id() === $user->id)
        @include('components.create-post-modal')
    @endif

    <style>
        /* Profile page specific styles */
        .profile-stats {
            background: linear-gradient(135deg, #7BC74D 0%, #393E46 100%);
        }

        .profile-cover {
            background: linear-gradient(135deg, #7BC74D 0%, #393E46 100%);
            position: relative;
            overflow: hidden;
        }

        .profile-cover::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.05"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .profile-avatar-border {
            box-shadow: 0 0 0 4px white, 0 0 20px rgba(0, 0, 0, 0.15);
            transition: all 0.3s ease;
        }

        .profile-avatar-border:hover {
            box-shadow: 0 0 0 4px white, 0 0 30px rgba(123, 199, 77, 0.3);
        }

        .hover-lift {
            transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        }

        .hover-lift:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        /* Prevent content overflow */
        .profile-content {
            max-width: 100%;
            overflow-x: hidden;
        }

        /* Responsive text truncation */
        .line-clamp-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        /* Smooth transitions for interactive elements */
        .transition-all {
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* Custom scrollbar for activity section */
        .activity-scroll::-webkit-scrollbar {
            width: 6px;
        }

        .activity-scroll::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 3px;
        }

        .activity-scroll::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }

        .activity-scroll::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }

        /* Mobile responsive improvements */
        @media (max-width: 640px) {
            .profile-cover {
                height: 12rem;
            }

            .profile-avatar-border {
                width: 5rem;
                height: 5rem;
            }
        }

        /* Ensure proper spacing and prevent layout shifts */
        .profile-grid {
            display: grid;
            gap: 1.5rem;
            grid-template-columns: 1fr;
        }

        @media (min-width: 1024px) {
            .profile-grid {
                grid-template-columns: 1fr 3fr;
            }
        }
    </style>

    <script>
        // Background photo upload functionality
        function uploadBackgroundPhoto() {
            const form = document.getElementById('backgroundPhotoForm');
            const fileInput = document.getElementById('backgroundPhotoInput');

            if (fileInput.files.length > 0) {
                // Show loading state
                const uploadButton = document.querySelector('button[onclick="document.getElementById(\'backgroundPhotoInput\').click()"]');
                const originalText = uploadButton.innerHTML;
                uploadButton.innerHTML = '<svg class="w-4 h-4 inline mr-2 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path></svg><span class="hidden sm:inline">Uploading...</span>';
                uploadButton.disabled = true;

                // Submit the form
                form.submit();
            }
        }

        // Background photo delete functionality
        function deleteBackgroundPhoto() {
            if (confirm('Are you sure you want to remove your background photo?')) {
                // Create and submit delete form
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = '{{ route("profile.background-photo.delete") }}';

                const csrfToken = document.createElement('input');
                csrfToken.type = 'hidden';
                csrfToken.name = '_token';
                csrfToken.value = '{{ csrf_token() }}';

                const methodField = document.createElement('input');
                methodField.type = 'hidden';
                methodField.name = '_method';
                methodField.value = 'DELETE';

                form.appendChild(csrfToken);
                form.appendChild(methodField);
                document.body.appendChild(form);

                // Show loading state
                const deleteButton = document.querySelector('button[onclick="deleteBackgroundPhoto()"]');
                const originalText = deleteButton.innerHTML;
                deleteButton.innerHTML = '<svg class="w-4 h-4 inline mr-1 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path></svg><span class="hidden sm:inline">Removing...</span>';
                deleteButton.disabled = true;

                form.submit();
            }
        }

        // File input validation (only if element exists - i.e., viewing own profile)
        const backgroundPhotoInput = document.getElementById('backgroundPhotoInput');
        if (backgroundPhotoInput) {
            backgroundPhotoInput.addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (file) {
                    // Check file size (5MB limit)
                    if (file.size > 5 * 1024 * 1024) {
                        alert('File size must be less than 5MB');
                        e.target.value = '';
                        return;
                    }

                    // Check file type
                    const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg', 'image/gif'];
                    if (!allowedTypes.includes(file.type)) {
                        alert('Please select a valid image file (JPEG, PNG, JPG, or GIF)');
                        e.target.value = '';
                        return;
                    }
                }
            });
        }
    </script>
</x-unilink-layout>
