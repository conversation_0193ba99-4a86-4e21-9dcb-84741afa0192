<?php

namespace App\Events;

use App\Models\User;
use App\Models\Post;
use App\Models\Comment;

use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class PostCommentAdded
{
    use Dispatchable, SerializesModels;

    public $user;
    public $post;
    public $comment;

    /**
     * Create a new event instance.
     */
    public function __construct(User $user, Post $post, Comment $comment)
    {
        $this->user = $user;
        $this->post = $post;
        $this->comment = $comment;
    }


}
