<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Reaction>
 */
class ReactionFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'type' => $this->faker->randomElement(['like', 'love', 'haha', 'wow', 'sad', 'angry']),
            'user_id' => \App\Models\User::factory(),
            'reactable_type' => \App\Models\Post::class,
            'reactable_id' => \App\Models\Post::factory(),
        ];
    }
}
