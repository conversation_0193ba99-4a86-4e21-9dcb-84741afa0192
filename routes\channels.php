<?php

use Illuminate\Support\Facades\Broadcast;

/*
|--------------------------------------------------------------------------
| Broadcast Channels
|--------------------------------------------------------------------------
|
| Here you may register all of the event broadcasting channels that your
| application supports. The given channel authorization callbacks are
| used to check if an authenticated user can listen to the channel.
|
*/

Broadcast::channel('App.Models.User.{id}', function ($user, $id) {
    return (int) $user->id === (int) $id;
});

Broadcast::channel('notifications.{userId}', function ($user, $userId) {
    return (int) $user->id === (int) $userId;
});

Broadcast::channel('organization.{organizationId}', function ($user, $organizationId) {
    // Check if user is a member or follower of the organization
    return $user->organizations()->where('organization_id', $organizationId)->exists() ||
           $user->followedOrganizations()->where('organization_id', $organizationId)->exists();
});

Broadcast::channel('group.{groupId}', function ($user, $groupId) {
    // Check if user is a member of the group
    return $user->groups()->where('group_id', $groupId)->exists();
});
